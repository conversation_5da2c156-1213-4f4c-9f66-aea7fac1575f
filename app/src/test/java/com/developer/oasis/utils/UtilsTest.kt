package com.developer.oasis.utils

import org.junit.Assert.*
import org.junit.Test

/**
 * Utils工具类单元测试
 */
class UtilsTest {
    
    @Test
    fun testIsNullOrEmptyString() {
        // 测试null字符串
        assertTrue(Utils.isNullOrEmptyString(null))
        
        // 测试空字符串
        assertTrue(Utils.isNullOrEmptyString(""))
        
        // 测试空白字符串
        assertTrue(Utils.isNullOrEmptyString("   "))
        
        // 测试正常字符串
        assertFalse(Utils.isNullOrEmptyString("test"))
        assertFalse(Utils.isNullOrEmptyString("  test  "))
    }
    
    @Test
    fun testGetCorrectPhoneNumber() {
        // 测试韩国号码格式转换
        assertEquals("010-1234-5678", Utils.getCorrectPhoneNumber("+82-10-1234-5678"))
        assertEquals("010-1234-5678", Utils.getCorrectPhoneNumber("82-10-1234-5678"))
        assertEquals("010-1234-5678", Utils.getCorrectPhoneNumber("010-1234-5678"))
        
        // 测试特殊字符移除
        assertEquals("01012345678", Utils.getCorrectPhoneNumber("010-1234-5678"))
        assertEquals("01012345678", Utils.getCorrectPhoneNumber("010 1234 5678"))
        assertEquals("01012345678", Utils.getCorrectPhoneNumber("010.1234.5678"))
        
        // 测试空字符串
        assertEquals("", Utils.getCorrectPhoneNumber(""))
        assertEquals("", Utils.getCorrectPhoneNumber("   "))
    }
    
    @Test
    fun testGetServerUrl() {
        val url = Utils.getServerUrl()
        assertNotNull(url)
        assertTrue(url.startsWith("http"))
        assertTrue(url.endsWith("/"))
    }
    
    @Test
    fun testFormatDateTime() {
        val utils = Utils.getInstance()
        val timestamp = 1642204800000L // 2022-01-15 00:00:00
        
        // 测试默认格式
        val defaultFormat = utils.formatDateTime(timestamp)
        assertEquals("2022-01-15 00:00:00", defaultFormat)
        
        // 测试自定义格式
        val customFormat = utils.formatDateTime(timestamp, "yyyy-MM-dd")
        assertEquals("2022-01-15", customFormat)
        
        // 测试时间格式
        val timeFormat = utils.formatDateTime(timestamp, "HH:mm:ss")
        assertEquals("00:00:00", timeFormat)
    }
    
    @Test
    fun testIsToday() {
        val utils = Utils.getInstance()
        val now = System.currentTimeMillis()
        val yesterday = now - 24 * 60 * 60 * 1000L
        val tomorrow = now + 24 * 60 * 60 * 1000L
        
        // 测试今天
        assertTrue(utils.isToday(now))
        
        // 测试昨天
        assertFalse(utils.isToday(yesterday))
        
        // 测试明天
        assertFalse(utils.isToday(tomorrow))
    }
}
