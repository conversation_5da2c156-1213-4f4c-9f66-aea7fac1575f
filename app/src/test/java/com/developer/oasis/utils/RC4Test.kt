package com.developer.oasis.utils

import org.junit.Assert.*
import org.junit.Test

/**
 * RC4加密工具类单元测试
 */
class RC4Test {
    
    private val testKey = "testkey123"
    private val testData = "Hello, World! 这是测试数据。"
    
    @Test
    fun testEncryptDecrypt() {
        // 测试加密
        val encrypted = RC4.encrypt(testData, testKey)
        assertNotNull(encrypted)
        assertNotEquals(testData, encrypted)
        assertTrue(encrypted.isNotEmpty())
        
        // 测试解密
        val decrypted = RC4.decrypt(encrypted, testKey)
        assertEquals(testData, decrypted)
    }
    
    @Test
    fun testEncryptDecryptBytes() {
        val dataBytes = testData.toByteArray(Charsets.UTF_8)
        val keyBytes = testKey.toByteArray(Charsets.UTF_8)
        
        // 测试字节数组加密
        val encrypted = RC4.encrypt(dataBytes, keyBytes)
        assertNotNull(encrypted)
        assertFalse(encrypted.contentEquals(dataBytes))
        
        // 测试字节数组解密
        val decrypted = RC4.decrypt(encrypted, keyBytes)
        assertArrayEquals(dataBytes, decrypted)
    }
    
    @Test
    fun testEmptyData() {
        // 测试空字符串
        val encrypted = RC4.encrypt("", testKey)
        val decrypted = RC4.decrypt(encrypted, testKey)
        assertEquals("", decrypted)
    }
    
    @Test
    fun testDifferentKeys() {
        val key1 = "key1"
        val key2 = "key2"
        
        val encrypted1 = RC4.encrypt(testData, key1)
        val encrypted2 = RC4.encrypt(testData, key2)
        
        // 不同密钥应该产生不同的加密结果
        assertNotEquals(encrypted1, encrypted2)
        
        // 用错误的密钥解密应该得到错误的结果
        val wrongDecrypted = RC4.decrypt(encrypted1, key2)
        assertNotEquals(testData, wrongDecrypted)
    }
    
    @Test
    fun testConsistency() {
        // 多次加密同样的数据应该得到相同的结果
        val encrypted1 = RC4.encrypt(testData, testKey)
        val encrypted2 = RC4.encrypt(testData, testKey)
        assertEquals(encrypted1, encrypted2)
        
        // 多次解密应该得到相同的结果
        val decrypted1 = RC4.decrypt(encrypted1, testKey)
        val decrypted2 = RC4.decrypt(encrypted2, testKey)
        assertEquals(decrypted1, decrypted2)
        assertEquals(testData, decrypted1)
    }
    
    @Test
    fun testLargeData() {
        // 测试大数据加密解密
        val largeData = "A".repeat(10000)
        val encrypted = RC4.encrypt(largeData, testKey)
        val decrypted = RC4.decrypt(encrypted, testKey)
        assertEquals(largeData, decrypted)
    }
    
    @Test
    fun testSpecialCharacters() {
        // 测试特殊字符
        val specialData = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~\n\t\r"
        val encrypted = RC4.encrypt(specialData, testKey)
        val decrypted = RC4.decrypt(encrypted, testKey)
        assertEquals(specialData, decrypted)
    }
    
    @Test
    fun testUnicodeCharacters() {
        // 测试Unicode字符
        val unicodeData = "测试数据 🚀 🎉 ✨ 한글 日本語 العربية"
        val encrypted = RC4.encrypt(unicodeData, testKey)
        val decrypted = RC4.decrypt(encrypted, testKey)
        assertEquals(unicodeData, decrypted)
    }
}
