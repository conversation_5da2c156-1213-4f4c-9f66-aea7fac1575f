package com.developer.oasis.data

import org.junit.Assert.*
import org.junit.Test

/**
 * 数据类单元测试
 */
class DataClassTest {
    
    @Test
    fun testBlockNumberData() {
        val blockData = BlockNumberData("010-1234-5678", 1, 5)
        
        assertEquals("010-1234-5678", blockData.phoneNumber)
        assertEquals(1, blockData.nBlockType)
        assertEquals(5, blockData.nTodayCount)
        
        // 测试常量
        assertEquals(0, BlockNumberData.BLOCK_TYPE_SPEC)
        assertEquals(1, BlockNumberData.BLOCK_TYPE_PREF)
        assertEquals(2, BlockNumberData.BLOCK_TYPE_EXPL)
    }
    
    @Test
    fun testBlockNumberHistory() {
        val history = BlockNumberHistory("010-1234-5678", 3, System.currentTimeMillis())
        
        assertEquals("010-1234-5678", history.number)
        assertEquals(3, history.type)
        assertTrue(history.dateTick > 0)
        
        // 测试常量
        assertEquals(1, BlockNumberHistory.TYPE_UNKNOWN)
        assertEquals(2, BlockNumberHistory.TYPE_TODAYCALL)
        assertEquals(3, BlockNumberHistory.TYPE_SPECNUM)
        assertEquals(4, BlockNumberHistory.TYPE_PREFNUM)
        assertEquals(5, BlockNumberHistory.TYPE_ALL)
        assertEquals(6, BlockNumberHistory.TYPE_CALLEXP)
    }
    
    @Test
    fun testUserInfo() {
        val userInfo = UserInfo("<EMAIL>", "password123", "device_token")
        
        assertEquals("<EMAIL>", userInfo.userID)
        assertEquals("password123", userInfo.userPass)
        assertEquals("device_token", userInfo.devToken)
        
        // 测试默认值
        val defaultUserInfo = UserInfo()
        assertEquals("", defaultUserInfo.userID)
        assertEquals("", defaultUserInfo.userPass)
        assertEquals("", defaultUserInfo.devToken)
    }
    
    @Test
    fun testRecentCallData() {
        val callData = RecentCallData("010-1234-5678", 1, "2024-01-15", "来电")
        
        assertEquals("010-1234-5678", callData.phonenumber)
        assertEquals(1, callData.callType)
        assertEquals("2024-01-15", callData.date)
        assertEquals("来电", callData.memo)
        
        // 测试默认备注
        val callDataWithoutMemo = RecentCallData("010-1234-5678", 1, "2024-01-15")
        assertEquals("", callDataWithoutMemo.memo)
    }
    
    @Test
    fun testRecentIncomeInfo() {
        val incomeInfo1 = RecentIncomeInfo("010-1234-5678", "张三", "2024-01-15", 1, 1, "#FF0000")
        val incomeInfo2 = RecentIncomeInfo("010-8765-4321", "李四", "2024-01-14", 2, 2, "#00FF00")
        
        assertEquals("010-1234-5678", incomeInfo1.phoneNumber)
        assertEquals("张三", incomeInfo1.contactName)
        assertEquals("2024-01-15", incomeInfo1.callDate)
        
        // 测试Comparable接口
        assertTrue(incomeInfo1.compareTo(incomeInfo2) < 0) // 日期倒序，较新的日期应该小于较旧的
        
        // 测试equals方法
        val incomeInfo3 = RecentIncomeInfo("010-1234-5678", "王五", "2024-01-16", 3, 3)
        assertTrue(incomeInfo1.equals(incomeInfo3)) // 相同电话号码应该相等
        assertFalse(incomeInfo1.equals(incomeInfo2)) // 不同电话号码应该不相等
    }
    
    @Test
    fun testNoticeInfo() {
        val notice = NoticeInfo(1, "系统通知", "这是一条测试通知", "2024-01-15")
        
        assertEquals(1, notice.id)
        assertEquals("系统通知", notice.subject)
        assertEquals("这是一条测试通知", notice.content)
        assertEquals("2024-01-15", notice.date)
    }
    
    @Test
    fun testDrawerData() {
        val drawerData = DrawerData("菜单项", 123, "5")
        
        assertEquals("菜单项", drawerData.name)
        assertEquals(123, drawerData.icon)
        assertEquals("5", drawerData.cnt)
        
        // 测试默认值
        val defaultDrawerData = DrawerData("菜单项")
        assertEquals("菜单项", defaultDrawerData.name)
        assertNull(defaultDrawerData.icon)
        assertEquals("", defaultDrawerData.cnt)
    }
    
    @Test
    fun testPhoneInfo() {
        val phoneInfo = PhoneInfo(1, "010-1234-5678", "张三", System.currentTimeMillis())
        
        assertEquals(1, phoneInfo.id)
        assertEquals("010-1234-5678", phoneInfo.phoneNumber)
        assertEquals("张三", phoneInfo.userName)
        assertTrue(phoneInfo.updatetime > 0)
        
        // 测试格式化时间方法
        val timeString = phoneInfo.getUpdateTimeString()
        assertNotNull(timeString)
        assertTrue(timeString.contains("-"))
        assertTrue(timeString.contains(":"))
        
        // 测试equals和hashCode
        val phoneInfo2 = PhoneInfo(2, "010-1234-5678", "张三", System.currentTimeMillis())
        assertTrue(phoneInfo.equals(phoneInfo2)) // 相同用户名和电话号码应该相等
        assertEquals(phoneInfo.hashCode(), phoneInfo2.hashCode())
    }
    
    @Test
    fun testSearchResultData() {
        val searchResult = SearchResultData(1, 0xFF00FF00.toInt(), "移动通信", "2024-01-15", "正常号码")
        
        assertEquals(1, searchResult.action)
        assertEquals(0xFF00FF00.toInt(), searchResult.color)
        assertEquals("移动通信", searchResult.compamy) // 注意保持原始拼写
        assertEquals("2024-01-15", searchResult.date)
        assertEquals("正常号码", searchResult.memo)
    }
    
    @Test
    fun testDataClassCopy() {
        // 测试数据类的copy功能
        val original = BlockNumberData("010-1234-5678", 1, 5)
        val copied = original.copy(nTodayCount = 10)
        
        assertEquals(original.phoneNumber, copied.phoneNumber)
        assertEquals(original.nBlockType, copied.nBlockType)
        assertEquals(10, copied.nTodayCount)
        assertNotEquals(original.nTodayCount, copied.nTodayCount)
    }
    
    @Test
    fun testDataClassToString() {
        // 测试数据类的toString功能
        val blockData = BlockNumberData("010-1234-5678", 1, 5)
        val toString = blockData.toString()
        
        assertTrue(toString.contains("010-1234-5678"))
        assertTrue(toString.contains("1"))
        assertTrue(toString.contains("5"))
    }
}
