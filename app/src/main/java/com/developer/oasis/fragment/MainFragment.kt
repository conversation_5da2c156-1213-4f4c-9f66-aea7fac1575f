package com.developer.oasis.fragment

import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.*
import androidx.lifecycle.lifecycleScope
import com.developer.oasis.R
import com.developer.oasis.adapter.RecentCallListAdapter
import com.developer.oasis.data.RecentCallData
import com.developer.oasis.data.RecentIncomeInfo
import com.developer.oasis.model.CallLogListener
import com.developer.oasis.utils.*
import kotlinx.coroutines.launch

/**
 * 主页Fragment
 * 显示最近通话记录和快速搜索功能
 */
class MainFragment : BaseFragment(), CallLogListener {
    
    // UI组件
    private lateinit var editSearch: EditText
    private lateinit var lstRecentCall: ListView
    private lateinit var recentCallTV: TextView
    private lateinit var incomeEmptyTV: TextView
    private lateinit var layoutWatermark: FrameLayout
    
    // 数据和适配器
    private val recentCallDataItems = mutableListOf<RecentCallData>()
    private lateinit var recentCallListAdapter: RecentCallListAdapter
    
    // 根视图
    private var rootView: View? = null
    
    companion object {
        @Volatile
        private var instance: MainFragment? = null
        
        fun getInstance(): MainFragment? = instance
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        instance = this
        
        if (rootView == null) {
            rootView = inflater.inflate(R.layout.fragment_main, container, false)
            initUI(rootView!!)
        }
        
        baseActivity?.hideSoftKeyboard()
        return rootView
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 刷新通话记录
        refreshCallLog()
    }
    
    override fun onResume() {
        super.onResume()
        
        // 更新查询次数显示
        updateQueryCountDisplay()
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        // 初始化视图组件
        editSearch = view.findViewById(R.id.edtSearch)
        lstRecentCall = view.findViewById(R.id.lstRecentCall)
        recentCallTV = view.findViewById(R.id.recentCallTV)
        incomeEmptyTV = view.findViewById(R.id.incomeEmptyTV)
        layoutWatermark = view.findViewById(R.id.layoutWatermark)
        
        // 设置搜索框监听器
        setupSearchEditText()
        
        // 初始化通话记录列表
        setupRecentCallList()
        
        // 设置水印
        setupWatermark()
    }
    
    /**
     * 设置搜索框
     */
    private fun setupSearchEditText() {
        editSearch.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH || 
                (event?.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                performSearch()
                true
            } else {
                false
            }
        }
        
        // 设置搜索提示
        editSearch.hint = getString(R.string.search_hint)
    }
    
    /**
     * 设置通话记录列表
     */
    private fun setupRecentCallList() {
        recentCallListAdapter = RecentCallListAdapter(requireContext(), recentCallDataItems)
        lstRecentCall.adapter = recentCallListAdapter
        
        // 设置列表项点击监听
        lstRecentCall.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            if (position < recentCallDataItems.size) {
                val callData = recentCallDataItems[position]
                searchPhoneNumber(callData.phonenumber)
            }
        }
    }
    
    /**
     * 设置水印
     */
    private fun setupWatermark() {
        try {
            val utilAuth = UtilAuth.getInstance(requireContext())
            // 可以在这里设置水印信息
            // layoutWatermark.background = ...
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 执行搜索
     */
    private fun performSearch() {
        val searchText = editSearch.text.toString().trim()
        
        if (searchText.isEmpty()) {
            showToast(getString(R.string.search_hint))
            return
        }
        
        // 检查查询次数
        if (Global.remainQueryCount <= 0 && !Global.searchHistory) {
            showAlertDialog("错误", "今日查询次数已用完")
            return
        }
        
        searchPhoneNumber(searchText)
    }
    
    /**
     * 搜索电话号码
     */
    private fun searchPhoneNumber(phoneNumber: String) {
        val correctedNumber = Utils.getCorrectPhoneNumber(phoneNumber)
        Global.incomingCallNumber = correctedNumber
        
        baseActivity?.gotoSearchFragment()
        baseActivity?.hideSoftKeyboard()
        
        // 清空搜索框
        editSearch.setText("")
    }
    
    /**
     * 刷新通话记录
     */
    fun refreshCallLog() {
        lifecycleScope.launch {
            try {
                showProgress("加载通话记录...")
                
                // 获取通话记录
                val callLogs = loadRecentCallLogs()
                
                safeExecute {
                    recentCallDataItems.clear()
                    recentCallDataItems.addAll(callLogs)
                    recentCallListAdapter.notifyDataSetChanged()
                    
                    // 更新空状态显示
                    updateEmptyState()
                    
                    dismissProgress()
                }
                
            } catch (e: Exception) {
                safeExecute {
                    dismissProgress()
                    showToast("加载通话记录失败")
                }
            }
        }
    }
    
    /**
     * 加载最近通话记录
     */
    private suspend fun loadRecentCallLogs(): List<RecentCallData> {
        // 这里应该从系统通话记录中读取数据
        // 暂时返回模拟数据
        return listOf(
            RecentCallData("010-1234-5678", 1, "2024-01-15 10:30", "来电"),
            RecentCallData("010-8765-4321", 2, "2024-01-15 09:15", "去电"),
            RecentCallData("010-1111-2222", 3, "2024-01-14 18:45", "未接")
        )
    }
    
    /**
     * 更新空状态显示
     */
    private fun updateEmptyState() {
        if (recentCallDataItems.isEmpty()) {
            lstRecentCall.visibility = View.GONE
            incomeEmptyTV.visibility = View.VISIBLE
            incomeEmptyTV.text = "暂无通话记录"
        } else {
            lstRecentCall.visibility = View.VISIBLE
            incomeEmptyTV.visibility = View.GONE
        }
    }
    
    /**
     * 更新查询次数显示
     */
    private fun updateQueryCountDisplay() {
        try {
            val remainCount = Global.remainQueryCount
            recentCallTV.text = "最近通话 (剩余查询: $remainCount)"
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    // CallLogListener 接口实现
    override fun onCallLogChanged() {
        refreshCallLog()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        instance = null
        rootView = null
    }
}
