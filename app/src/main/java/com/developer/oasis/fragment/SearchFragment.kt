package com.developer.oasis.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.lifecycle.lifecycleScope
import com.developer.oasis.R
import com.developer.oasis.adapter.SearchResultListAdapter
import com.developer.oasis.data.SearchResultData
import com.developer.oasis.utils.*
import kotlinx.coroutines.launch

/**
 * 电话号码搜索Fragment
 * 提供号码查询和搜索历史功能
 */
class SearchFragment : BaseFragment() {
    
    // UI组件
    private lateinit var txtPhoneNumber: TextView
    private lateinit var txtSearchResult: TextView
    private lateinit var lstSearchResult: ListView
    private lateinit var btnSearch: Button
    private lateinit var progressBar: ProgressBar
    
    // 数据和适配器
    private val searchResults = mutableListOf<SearchResultData>()
    private lateinit var searchResultAdapter: SearchResultListAdapter
    
    // 当前搜索的号码
    private var currentPhoneNumber: String = ""
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_search, container, false)
        initUI(view)
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 获取要搜索的号码
        currentPhoneNumber = Global.incomingCallNumber
        if (currentPhoneNumber.isNotEmpty()) {
            displayPhoneNumber(currentPhoneNumber)
            performSearch(currentPhoneNumber)
        }
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        txtPhoneNumber = view.findViewById(R.id.txtPhoneNumber)
        txtSearchResult = view.findViewById(R.id.txtSearchResult)
        lstSearchResult = view.findViewById(R.id.lstSearchResult)
        btnSearch = view.findViewById(R.id.btnSearch)
        progressBar = view.findViewById(R.id.progressBar)
        
        // 初始化搜索结果列表
        searchResultAdapter = SearchResultListAdapter(requireContext(), searchResults)
        lstSearchResult.adapter = searchResultAdapter
        
        // 设置按钮点击监听
        btnSearch.setOnClickListener {
            if (currentPhoneNumber.isNotEmpty()) {
                performSearch(currentPhoneNumber)
            }
        }
        
        // 设置列表项点击监听
        lstSearchResult.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            if (position < searchResults.size) {
                val result = searchResults[position]
                showSearchResultDetail(result)
            }
        }
    }
    
    /**
     * 显示电话号码
     */
    private fun displayPhoneNumber(phoneNumber: String) {
        txtPhoneNumber.text = Utils.getCorrectPhoneNumber(phoneNumber)
    }
    
    /**
     * 执行搜索
     */
    private fun performSearch(phoneNumber: String) {
        lifecycleScope.launch {
            try {
                showSearchProgress(true)
                txtSearchResult.text = "正在查询..."
                
                // 检查查询次数
                if (Global.remainQueryCount <= 0 && !Global.searchHistory) {
                    showAlertDialog("查询限制", "今日查询次数已用完")
                    showSearchProgress(false)
                    return@launch
                }
                
                // 执行查询
                val result = searchPhoneNumber(phoneNumber)
                
                safeExecute {
                    showSearchProgress(false)
                    
                    if (result.isSuccess) {
                        val searchData = result.getOrNull()
                        displaySearchResults(searchData)
                        
                        // 减少查询次数
                        if (!Global.searchHistory) {
                            Global.remainQueryCount--
                        }
                    } else {
                        txtSearchResult.text = "查询失败: ${result.exceptionOrNull()?.message}"
                        clearSearchResults()
                    }
                }
                
            } catch (e: Exception) {
                safeExecute {
                    showSearchProgress(false)
                    txtSearchResult.text = "查询出错"
                    showToast("查询过程中发生错误")
                }
            }
        }
    }
    
    /**
     * 搜索电话号码
     */
    private suspend fun searchPhoneNumber(phoneNumber: String): Result<List<SearchResultData>> {
        return try {
            val utilContact = UtilContact.getInstance(requireContext())
            val queryResult = utilContact.queryPhoneNumber(
                phoneNumber = phoneNumber,
                includeToday = true,
                queryType = UtilContact.QUERYTYPE_ONQUERY,
                todayCallLimit = 0
            )
            
            if (queryResult.isSuccess) {
                // 解析查询结果并转换为SearchResultData列表
                val results = parseSearchResults(queryResult.getOrNull() ?: "")
                Result.success(results)
            } else {
                Result.failure(queryResult.exceptionOrNull() ?: Exception("查询失败"))
            }
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 解析搜索结果
     */
    private fun parseSearchResults(resultJson: String): List<SearchResultData> {
        // 这里应该解析实际的JSON结果
        // 暂时返回模拟数据
        return listOf(
            SearchResultData(1, 0xFF00FF00.toInt(), "移动通信", "2024-01-15", "正常号码"),
            SearchResultData(2, 0xFFFF0000.toInt(), "骚扰电话", "2024-01-15", "多次举报"),
            SearchResultData(0, 0xFF0000FF.toInt(), "未知", "2024-01-15", "暂无信息")
        )
    }
    
    /**
     * 显示搜索结果
     */
    private fun displaySearchResults(results: List<SearchResultData>?) {
        if (results.isNullOrEmpty()) {
            txtSearchResult.text = "未找到相关信息"
            clearSearchResults()
        } else {
            txtSearchResult.text = "找到 ${results.size} 条结果"
            searchResults.clear()
            searchResults.addAll(results)
            searchResultAdapter.notifyDataSetChanged()
            
            lstSearchResult.visibility = View.VISIBLE
        }
    }
    
    /**
     * 清空搜索结果
     */
    private fun clearSearchResults() {
        searchResults.clear()
        searchResultAdapter.notifyDataSetChanged()
        lstSearchResult.visibility = View.GONE
    }
    
    /**
     * 显示搜索结果详情
     */
    private fun showSearchResultDetail(result: SearchResultData) {
        val message = """
            公司: ${result.compamy}
            日期: ${result.date}
            备注: ${result.memo}
        """.trimIndent()
        
        showAlertDialog("详细信息", message)
    }
    
    /**
     * 显示/隐藏搜索进度
     */
    private fun showSearchProgress(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        btnSearch.isEnabled = !show
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        // 清空全局来电号码
        Global.incomingCallNumber = ""
    }
}
