package com.developer.oasis.fragment

import android.app.AlertDialog
import android.app.ProgressDialog
import android.os.Bundle
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.developer.oasis.activity.BaseActivity
import com.developer.oasis.utils.Utils

/**
 * 所有Fragment的基类
 * 提供通用功能：进度对话框、警告对话框、Toast消息、Activity引用管理
 */
abstract class BaseFragment : Fragment() {
    
    protected var baseActivity: BaseActivity? = null
    private var progressDialog: ProgressDialog? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        baseActivity = activity as? BaseActivity
    }
    
    override fun onResume() {
        super.onResume()
        try {
            baseActivity?.let { activity ->
                activity.hideSoftKeyboard()
                activity.invalidateOptionsMenu()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        dismissProgress()
    }
    
    /**
     * 显示警告对话框
     */
    protected fun showAlertDialog(title: String, message: String) {
        context?.let { ctx ->
            AlertDialog.Builder(ctx)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定", null)
                .show()
        }
    }
    
    /**
     * 显示确认对话框
     */
    protected fun showConfirmDialog(
        title: String, 
        message: String, 
        onConfirm: () -> Unit,
        onCancel: (() -> Unit)? = null
    ) {
        context?.let { ctx ->
            AlertDialog.Builder(ctx)
                .setTitle(title)
                .setMessage(message)
                .setPositiveButton("确定") { _, _ -> onConfirm() }
                .setNegativeButton("取消") { _, _ -> onCancel?.invoke() }
                .show()
        }
    }
    
    /**
     * 显示Toast消息
     */
    protected fun showToast(message: String) {
        context?.let { ctx ->
            Toast.makeText(ctx, message, Toast.LENGTH_SHORT).show()
        }
    }
    
    /**
     * 显示长时间Toast消息
     */
    protected fun showLongToast(message: String) {
        context?.let { ctx ->
            Toast.makeText(ctx, message, Toast.LENGTH_LONG).show()
        }
    }
    
    /**
     * 显示进度对话框
     */
    protected fun showProgress(message: String) {
        context?.let { ctx ->
            dismissProgress()
            progressDialog = Utils.getInstance().openNewDialog(ctx, message, false, true)
            progressDialog?.show()
        }
    }
    
    /**
     * 隐藏进度对话框
     */
    protected fun dismissProgress() {
        try {
            progressDialog?.takeIf { it.isShowing }?.dismiss()
            progressDialog = null
        } catch (e: Exception) {
            // 忽略异常
        }
    }
    
    /**
     * 检查Fragment是否已添加到Activity
     */
    protected fun isFragmentAttached(): Boolean {
        return isAdded && activity != null && !isDetached
    }
    
    /**
     * 安全地执行UI操作
     */
    protected fun safeExecute(action: () -> Unit) {
        if (isFragmentAttached()) {
            try {
                action()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 获取字符串资源
     */
    protected fun getStringResource(resId: Int): String {
        return context?.getString(resId) ?: ""
    }
    
    /**
     * 获取字符串资源（带参数）
     */
    protected fun getStringResource(resId: Int, vararg formatArgs: Any): String {
        return context?.getString(resId, *formatArgs) ?: ""
    }
}
