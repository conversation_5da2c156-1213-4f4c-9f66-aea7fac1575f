package com.developer.oasis.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.lifecycle.lifecycleScope
import com.developer.oasis.R
import com.developer.oasis.adapter.NoticeListAdapter
import com.developer.oasis.data.NoticeInfo
import com.developer.oasis.utils.Global
import com.developer.oasis.utils.Utils
import kotlinx.coroutines.launch

/**
 * 通知公告Fragment
 * 显示应用通知和公告列表
 */
class NoticeFragment : BaseFragment() {
    
    // UI组件
    private lateinit var lstNotice: ListView
    private lateinit var txtNoNotice: TextView
    private lateinit var refreshButton: Button
    
    // 数据和适配器
    private val noticeItems = mutableListOf<NoticeInfo>()
    private lateinit var noticeAdapter: NoticeListAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_notice, container, false)
        initUI(view)
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 加载通知列表
        loadNoticeList()
        
        // 标记已读新通知
        markNoticeAsRead()
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        lstNotice = view.findViewById(R.id.lstNotice)
        txtNoNotice = view.findViewById(R.id.txtNoNotice)
        refreshButton = view.findViewById(R.id.refreshButton)
        
        // 初始化通知列表
        noticeAdapter = NoticeListAdapter(requireContext(), noticeItems)
        lstNotice.adapter = noticeAdapter
        
        // 设置刷新按钮点击监听
        refreshButton.setOnClickListener {
            refreshNoticeList()
        }
        
        // 设置列表项点击监听
        lstNotice.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            if (position < noticeItems.size) {
                val notice = noticeItems[position]
                showNoticeDetail(notice)
            }
        }
    }
    
    /**
     * 加载通知列表
     */
    private fun loadNoticeList() {
        lifecycleScope.launch {
            try {
                showProgress("加载通知...")
                
                // 从全局状态获取通知列表
                val notices = Global.noticeList.toList()
                
                safeExecute {
                    noticeItems.clear()
                    noticeItems.addAll(notices)
                    noticeAdapter.notifyDataSetChanged()
                    
                    updateEmptyState()
                    dismissProgress()
                }
                
            } catch (e: Exception) {
                safeExecute {
                    dismissProgress()
                    showToast("加载通知失败")
                }
            }
        }
    }
    
    /**
     * 刷新通知列表
     */
    private fun refreshNoticeList() {
        lifecycleScope.launch {
            try {
                showProgress("刷新通知...")
                
                // 从服务器获取最新通知
                val result = fetchNoticesFromServer()
                
                safeExecute {
                    dismissProgress()
                    
                    if (result.isSuccess) {
                        val notices = result.getOrNull() ?: emptyList()
                        
                        // 更新全局通知列表
                        Global.noticeList.clear()
                        Global.noticeList.addAll(notices)
                        
                        // 更新UI
                        noticeItems.clear()
                        noticeItems.addAll(notices)
                        noticeAdapter.notifyDataSetChanged()
                        
                        updateEmptyState()
                        showToast("刷新成功")
                    } else {
                        showToast("刷新失败: ${result.exceptionOrNull()?.message}")
                    }
                }
                
            } catch (e: Exception) {
                safeExecute {
                    dismissProgress()
                    showToast("刷新过程中发生错误")
                }
            }
        }
    }
    
    /**
     * 从服务器获取通知
     */
    private suspend fun fetchNoticesFromServer(): Result<List<NoticeInfo>> {
        return try {
            // 这里应该实现实际的网络请求
            // 暂时返回模拟数据
            val mockNotices = listOf(
                NoticeInfo(1, "系统维护通知", "系统将于今晚进行维护，请提前做好准备。", "2024-01-15"),
                NoticeInfo(2, "功能更新", "新增来电拦截功能，欢迎体验。", "2024-01-14"),
                NoticeInfo(3, "使用提醒", "请及时更新应用以获得最佳体验。", "2024-01-13")
            )
            
            Result.success(mockNotices)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 显示通知详情
     */
    private fun showNoticeDetail(notice: NoticeInfo) {
        val message = """
            发布日期: ${notice.date}
            
            ${notice.content}
        """.trimIndent()
        
        showAlertDialog(notice.subject, message)
    }
    
    /**
     * 更新空状态显示
     */
    private fun updateEmptyState() {
        if (noticeItems.isEmpty()) {
            lstNotice.visibility = View.GONE
            txtNoNotice.visibility = View.VISIBLE
            txtNoNotice.text = "暂无通知"
        } else {
            lstNotice.visibility = View.VISIBLE
            txtNoNotice.visibility = View.GONE
        }
    }
    
    /**
     * 标记通知为已读
     */
    private fun markNoticeAsRead() {
        Utils.getInstance().setNewNoticeState(requireContext(), false)
    }
}
