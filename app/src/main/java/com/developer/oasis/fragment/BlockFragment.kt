package com.developer.oasis.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import androidx.viewpager.widget.ViewPager
import com.developer.oasis.R
import com.google.android.material.tabs.TabLayout

/**
 * 拦截管理Fragment容器
 * 包含拦截设置、拦截号码列表、拦截历史三个子页面
 */
class BlockFragment : BaseFragment() {
    
    private lateinit var tabLayout: TabLayout
    private lateinit var viewPager: ViewPager
    private lateinit var pagerAdapter: BlockPagerAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_block, container, false)
        initUI(view)
        return view
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        tabLayout = view.findViewById(R.id.tabLayout)
        viewPager = view.findViewById(R.id.viewPager)
        
        setupViewPager()
        setupTabLayout()
    }
    
    /**
     * 设置ViewPager
     */
    private fun setupViewPager() {
        pagerAdapter = BlockPagerAdapter(childFragmentManager)
        viewPager.adapter = pagerAdapter
    }
    
    /**
     * 设置TabLayout
     */
    private fun setupTabLayout() {
        tabLayout.setupWithViewPager(viewPager)
        
        // 设置Tab标题
        tabLayout.getTabAt(0)?.text = "拦截设置"
        tabLayout.getTabAt(1)?.text = "拦截号码"
        tabLayout.getTabAt(2)?.text = "拦截历史"
    }
    
    /**
     * ViewPager适配器
     */
    private inner class BlockPagerAdapter(fm: FragmentManager) : FragmentPagerAdapter(fm, BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) {
        
        override fun getItem(position: Int): Fragment {
            return when (position) {
                0 -> BlockFragmentSetting()
                1 -> BlockFragmentNumbers()
                2 -> BlockFragmentHistory()
                else -> throw IllegalArgumentException("Invalid position: $position")
            }
        }
        
        override fun getCount(): Int = 3
        
        override fun getPageTitle(position: Int): CharSequence? {
            return when (position) {
                0 -> "拦截设置"
                1 -> "拦截号码"
                2 -> "拦截历史"
                else -> null
            }
        }
    }
}

/**
 * 拦截设置Fragment
 */
class BlockFragmentSetting : BaseFragment() {
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_block_setting, container, false)
        // TODO: 实现拦截设置UI
        return view
    }
}

/**
 * 拦截号码列表Fragment
 */
class BlockFragmentNumbers : BaseFragment() {
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_block_numbers, container, false)
        // TODO: 实现拦截号码列表UI
        return view
    }
}

/**
 * 拦截历史Fragment
 */
class BlockFragmentHistory : BaseFragment() {
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_block_history, container, false)
        // TODO: 实现拦截历史UI
        return view
    }
}
