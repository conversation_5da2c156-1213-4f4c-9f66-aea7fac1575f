package com.developer.oasis.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import com.developer.oasis.R
import com.developer.oasis.utils.UtilSetting

/**
 * 设置Fragment
 * 应用配置和偏好设置
 */
class SettingFragment : BaseFragment() {
    
    // UI组件
    private lateinit var switchTodayShow: Switch
    private lateinit var switchPopupRemain: Switch
    private lateinit var spinnerPopupPosition: Spinner
    private lateinit var btnSave: Button
    private lateinit var btnReset: Button
    
    // 工具类
    private lateinit var utilSetting: UtilSetting
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_setting, container, false)
        initUI(view)
        return view
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 初始化工具类
        utilSetting = UtilSetting.getInstance(requireContext())
        
        // 加载当前设置
        loadCurrentSettings()
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        switchTodayShow = view.findViewById(R.id.switchTodayShow)
        switchPopupRemain = view.findViewById(R.id.switchPopupRemain)
        spinnerPopupPosition = view.findViewById(R.id.spinnerPopupPosition)
        btnSave = view.findViewById(R.id.btnSave)
        btnReset = view.findViewById(R.id.btnReset)
        
        // 设置弹窗位置选项
        setupPopupPositionSpinner()
        
        // 设置按钮点击监听
        btnSave.setOnClickListener { saveSettings() }
        btnReset.setOnClickListener { resetSettings() }
    }
    
    /**
     * 设置弹窗位置下拉框
     */
    private fun setupPopupPositionSpinner() {
        val positions = arrayOf("左上角", "右上角", "左下角", "右下角")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, positions)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerPopupPosition.adapter = adapter
    }
    
    /**
     * 加载当前设置
     */
    private fun loadCurrentSettings() {
        try {
            switchTodayShow.isChecked = utilSetting.todayShow
            switchPopupRemain.isChecked = utilSetting.popupRemain
            spinnerPopupPosition.setSelection(utilSetting.popupPosition)
            
        } catch (e: Exception) {
            showToast("加载设置失败")
        }
    }
    
    /**
     * 保存设置
     */
    private fun saveSettings() {
        try {
            utilSetting.apply {
                todayShow = switchTodayShow.isChecked
                popupRemain = switchPopupRemain.isChecked
                popupPosition = spinnerPopupPosition.selectedItemPosition
                saveSetting()
            }
            
            showToast("设置已保存")
            
        } catch (e: Exception) {
            showToast("保存设置失败")
        }
    }
    
    /**
     * 重置设置
     */
    private fun resetSettings() {
        showConfirmDialog(
            "重置设置",
            "确定要重置所有设置为默认值吗？",
            onConfirm = {
                try {
                    utilSetting.resetToDefault()
                    loadCurrentSettings()
                    showToast("设置已重置")
                } catch (e: Exception) {
                    showToast("重置设置失败")
                }
            }
        )
    }
}

/**
 * 网页搜索Fragment
 */
class WebSearchFragment : BaseFragment() {
    
    private lateinit var webView: WebView
    private lateinit var progressBar: ProgressBar
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_web_search, container, false)
        initUI(view)
        return view
    }
    
    /**
     * 初始化UI组件
     */
    private fun initUI(view: View) {
        webView = view.findViewById(R.id.webView)
        progressBar = view.findViewById(R.id.progressBar)
        
        // 配置WebView
        setupWebView()
        
        // 加载搜索页面
        loadSearchPage()
    }
    
    /**
     * 配置WebView
     */
    private fun setupWebView() {
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
        }
        
        webView.webViewClient = object : WebViewClient() {
            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                progressBar.visibility = View.VISIBLE
            }
            
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                progressBar.visibility = View.GONE
            }
        }
    }
    
    /**
     * 加载搜索页面
     */
    private fun loadSearchPage() {
        val searchUrl = "https://www.google.com/search?q=${Global.incomingCallNumber}"
        webView.loadUrl(searchUrl)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        webView.destroy()
    }
}
