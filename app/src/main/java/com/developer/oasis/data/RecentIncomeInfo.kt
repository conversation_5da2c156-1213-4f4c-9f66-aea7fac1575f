package com.developer.oasis.data

/**
 * 最近来电信息数据模型
 * @param phoneNumber 电话号码
 * @param contactName 联系人姓名
 * @param callDate 通话日期
 * @param callType 通话类型
 * @param callTypeDetail 通话类型详情
 * @param color 显示颜色
 */
data class RecentIncomeInfo(
    val phoneNumber: String,
    val contactName: String = "",
    val callDate: String,
    val callType: Int,
    val callTypeDetail: Int,
    val color: String = ""
) : Comparable<RecentIncomeInfo> {
    
    override fun compareTo(other: RecentIncomeInfo): Int {
        // 按日期倒序排列
        return callDate.compareTo(other.callDate) * -1
    }
    
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (other !is RecentIncomeInfo) return false
        return phoneNumber == other.phoneNumber
    }
    
    override fun hashCode(): Int {
        return phoneNumber.hashCode()
    }
}
