package com.developer.oasis.data

import java.text.SimpleDateFormat
import java.util.*

/**
 * 电话信息数据模型
 * @param id 信息ID
 * @param phoneNumber 电话号码
 * @param userName 用户名称
 * @param updatetime 更新时间戳
 */
data class PhoneInfo(
    val id: Int,
    val phoneNumber: String,
    val userName: String,
    val updatetime: Long
) {
    /**
     * 获取格式化的更新时间字符串
     */
    fun getUpdateTimeString(): String {
        return try {
            val formatter = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            formatter.format(Date(updatetime))
        } catch (e: Exception) {
            e.printStackTrace()
            "2000-01-01 00:00:00"
        }
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is PhoneInfo) return false
        return userName == other.userName && phoneNumber == other.phoneNumber
    }
    
    override fun hashCode(): Int {
        var result = userName.hashCode()
        result = 31 * result + phoneNumber.hashCode()
        return result
    }
}
