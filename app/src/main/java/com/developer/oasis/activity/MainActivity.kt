package com.developer.oasis.activity

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.AdapterView
import android.widget.ListView
import android.widget.TextView
import androidx.appcompat.app.ActionBarDrawerToggle
import androidx.appcompat.widget.Toolbar
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import com.developer.oasis.R
import com.developer.oasis.adapter.DrawerAdapter
import com.developer.oasis.data.DrawerData
import com.developer.oasis.utils.*
import com.google.android.material.navigation.NavigationView

/**
 * 主Activity
 * 带导航抽屉的主应用界面，管理Fragment切换和用户信息显示
 */
class MainActivity : BaseActivity(), NavigationView.OnNavigationItemSelectedListener {
    
    // UI组件
    private lateinit var drawer: DrawerLayout
    private lateinit var drawerLV: ListView
    private lateinit var titleTV: TextView
    private lateinit var toolbarTB: Toolbar
    private lateinit var toggle: ActionBarDrawerToggle
    
    // 适配器和数据
    private lateinit var drawerAdapter: DrawerAdapter
    private val drawerItems = mutableListOf<DrawerData>()
    
    // 工具类
    private lateinit var backPressHandler: BackPressHandler
    
    // 导航抽屉点击监听器
    private val drawerItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
        handleDrawerItemClick(position)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        try {
            // 检查登录状态
            checkLoginStatus()
            
            // 初始化组件
            initViews()
            initToolbar()
            initDrawer()
            initFragment()
            initUserInfo()
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("MainActivity", "onCreate error", e)
        }
    }
    
    /**
     * 检查登录状态
     */
    private fun checkLoginStatus() {
        val utilAuth = UtilAuth.getInstance(this)
        if (utilAuth.userToken.isEmpty()) {
            // 未登录，跳转到登录页面
            val intent = Intent(this, LoginActivity::class.java)
            startActivity(intent)
            overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
            finish()
            return
        }
    }
    
    /**
     * 初始化视图组件
     */
    private fun initViews() {
        toolbarTB = findViewById(R.id.toolbar)
        titleTV = findViewById(R.id.titleTV)
        drawerLV = findViewById(R.id.drawerLV)
        drawer = findViewById(R.id.drawer_layout)
        
        // 初始化输入法管理器
        inputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        
        // 初始化返回按钮处理器
        backPressHandler = BackPressHandler(this)
    }
    
    /**
     * 初始化工具栏
     */
    private fun initToolbar() {
        setSupportActionBar(toolbarTB)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setHomeButtonEnabled(true)
        }
    }
    
    /**
     * 初始化导航抽屉
     */
    private fun initDrawer() {
        // 初始化抽屉数据
        drawerItems.clear()
        drawerItems.addAll(listOf(
            DrawerData("电话号码", R.drawable.ic_phone, ""),
            DrawerData("拦截管理", R.drawable.ic_block, ""),
            DrawerData("通知公告", R.drawable.ic_notice, getNoticeCount()),
            DrawerData("环境设置", R.drawable.ic_settings, ""),
            DrawerData("退出登录", R.drawable.ic_logout, "")
        ))
        
        // 设置适配器
        drawerAdapter = DrawerAdapter(this, drawerItems)
        drawerLV.adapter = drawerAdapter
        drawerLV.onItemClickListener = drawerItemClickListener
        
        // 设置抽屉切换
        toggle = ActionBarDrawerToggle(
            this,
            drawer,
            toolbarTB,
            R.string.navigation_drawer_open,
            R.string.navigation_drawer_close
        ).apply {
            // 抽屉状态监听
            setDrawerListener(object : DrawerLayout.DrawerListener {
                override fun onDrawerStateChanged(newState: Int) {
                    hideSoftKeyboard()
                }
                
                override fun onDrawerSlide(drawerView: View, slideOffset: Float) {}
                
                override fun onDrawerOpened(drawerView: View) {
                    invalidateOptionsMenu()
                }
                
                override fun onDrawerClosed(drawerView: View) {
                    invalidateOptionsMenu()
                }
            })
        }
        
        toggle.syncState()
        drawer.addDrawerListener(toggle)
    }
    
    /**
     * 初始化Fragment
     */
    private fun initFragment() {
        when {
            Global.incomingCallNumber.isNotEmpty() -> {
                // 有来电时显示搜索页面
                gotoSearchFragment()
            }
            Utils.getInstance().getNewNoticeState(this) -> {
                // 有新通知时显示通知页面
                gotoNoticeFragment()
            }
            else -> {
                // 默认显示主页
                gotoMainFragment()
            }
        }
    }
    
    /**
     * 初始化用户信息显示
     */
    private fun initUserInfo() {
        val utilAuth = UtilAuth.getInstance(this)
        
        findViewById<TextView>(R.id.txtCompany)?.text = utilAuth.userCompany
        findViewById<TextView>(R.id.txtRemainDay)?.text = "到期日: ${utilAuth.getLicenseEndDate()}"
    }
    
    /**
     * 处理导航抽屉项点击
     */
    private fun handleDrawerItemClick(position: Int) {
        when (position) {
            0 -> {
                // 电话号码
                gotoMainFragment()
                titleTV.text = getString(R.string.title_phoneNumber)
            }
            1 -> {
                // 拦截管理
                gotoBlockFragment()
                titleTV.text = "拦截管理"
            }
            2 -> {
                // 通知公告
                gotoNoticeFragmentEx()
                titleTV.text = "通知公告"
            }
            3 -> {
                // 环境设置
                gotoSettingFragment()
                titleTV.text = "环境设置"
            }
            4 -> {
                // 退出登录
                logout()
            }
        }
        
        drawer.closeDrawers()
    }
    
    /**
     * 获取通知数量
     */
    private fun getNoticeCount(): String {
        return if (Utils.getInstance().getNewNoticeState(this)) {
            "新"
        } else {
            ""
        }
    }
    
    /**
     * 退出登录
     */
    private fun logout() {
        val utilAuth = UtilAuth.getInstance(this)
        utilAuth.clearAuthInfo()
        
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right)
        finish()
    }
    
    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        // 处理导航视图项点击
        drawer.closeDrawer(GravityCompat.START)
        return true
    }
    
    override fun onBackPressed() {
        when {
            drawer.isDrawerOpen(GravityCompat.START) -> {
                drawer.closeDrawer(GravityCompat.START)
            }
            else -> {
                backPressHandler.onBackPressed("再次点击退出应用")
            }
        }
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return if (toggle.onOptionsItemSelected(item)) {
            true
        } else {
            super.onOptionsItemSelected(item)
        }
    }
    
    override fun onPostCreate(savedInstanceState: Bundle?) {
        super.onPostCreate(savedInstanceState)
        toggle.syncState()
    }
    
    override fun onResume() {
        super.onResume()
        
        // 更新通知状态
        updateNoticeStatus()
    }
    
    /**
     * 更新通知状态
     */
    private fun updateNoticeStatus() {
        val noticeCount = getNoticeCount()
        if (drawerItems.size > 2) {
            drawerItems[2] = drawerItems[2].copy(cnt = noticeCount)
            drawerAdapter.notifyDataSetChanged()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        backPressHandler.cancelToast()
    }
}
