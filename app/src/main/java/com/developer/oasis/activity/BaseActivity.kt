package com.developer.oasis.activity

import android.app.AlarmManager
import android.app.PendingIntent
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.developer.oasis.R
import com.developer.oasis.fragment.*
import com.developer.oasis.service.MainService
import com.developer.oasis.service.RestartReceiver
import com.developer.oasis.utils.*
import kotlinx.coroutines.*

/**
 * 所有Activity的基类
 * 提供通用功能：Fragment导航、进度对话框、Toast消息、软键盘管理等
 */
abstract class BaseActivity : AppCompatActivity() {
    
    protected var inputMethodManager: InputMethodManager? = null
    private var progressDialog: ProgressDialog? = null
    private val menuDelay = 300L // 菜单切换延迟
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // 初始化输入法管理器
        inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    }
    
    /**
     * 隐藏软键盘
     */
    fun hideSoftKeyboard(): Boolean {
        return try {
            currentFocus?.let { focus ->
                inputMethodManager?.hideSoftInputFromWindow(focus.windowToken, 0)
                true
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 显示进度对话框
     */
    fun showProgress(message: String) {
        try {
            dismissProgress()
            
            progressDialog = ProgressDialog(this).apply {
                setMessage(message)
                isIndeterminate = true
                setProgressStyle(ProgressDialog.STYLE_SPINNER)
                setCancelable(false)
                show()
            }
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("BaseActivity", "Failed to show progress", e)
        }
    }
    
    /**
     * 隐藏进度对话框
     */
    fun dismissProgress() {
        try {
            progressDialog?.takeIf { it.isShowing }?.dismiss()
            progressDialog = null
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("BaseActivity", "Failed to dismiss progress", e)
        }
    }
    
    /**
     * 显示Toast消息
     */
    protected fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 跳转到主页Fragment
     */
    fun gotoMainFragment() {
        val fragment = MainFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_MAIN
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 跳转到搜索Fragment
     */
    fun gotoSearchFragment() {
        val fragment = SearchFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_SEARCH
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 跳转到网页搜索Fragment
     */
    fun gotoWebSearchFragment() {
        val fragment = WebSearchFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_WEB
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 跳转到拦截管理Fragment
     */
    fun gotoBlockFragment() {
        val fragment = BlockFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_BLOCK
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 跳转到通知Fragment
     */
    fun gotoNoticeFragment() {
        Utils.getInstance().setNewNoticeState(this, false)
        val fragment = NoticeFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_NOTICE
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 跳转到通知Fragment（扩展版本）
     */
    fun gotoNoticeFragmentEx() {
        gotoNoticeFragment()
    }
    
    /**
     * 跳转到设置Fragment
     */
    fun gotoSettingFragment() {
        val fragment = SettingFragment()
        Global.fragmentState = Const.FRAGMENT_STATE_SETTING
        
        Handler(Looper.getMainLooper()).postDelayed({
            supportFragmentManager.beginTransaction()
                .replace(R.id.mainLayout, fragment)
                .commit()
        }, menuDelay)
    }
    
    /**
     * 启动主服务
     */
    fun startMyService() {
        try {
            val serviceIntent = Intent(this, MainService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }
            
            // 设置定时重启服务的闹钟
            setupServiceRestartAlarm()
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("BaseActivity", "Failed to start service", e)
        }
    }
    
    /**
     * 设置服务重启闹钟
     */
    private fun setupServiceRestartAlarm() {
        try {
            val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val restartIntent = Intent(this, RestartReceiver::class.java).apply {
                action = RestartReceiver.ACTION_RESTART_ALARM_SERVICE
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                this, 
                0, 
                restartIntent, 
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // 每30分钟重启一次服务
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                System.currentTimeMillis(),
                30 * 60 * 1000L, // 30分钟
                pendingIntent
            )
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("BaseActivity", "Failed to setup restart alarm", e)
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        dismissProgress()
    }
}
