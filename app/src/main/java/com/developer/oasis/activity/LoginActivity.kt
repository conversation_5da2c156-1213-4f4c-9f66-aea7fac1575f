package com.developer.oasis.activity

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.View
import android.widget.Button
import android.widget.EditText
import androidx.activity.result.contract.ActivityResultContracts
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.developer.oasis.R
import com.developer.oasis.data.UserInfo
import com.developer.oasis.utils.*
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * 用户登录Activity
 * 提供邮箱/密码认证、设备令牌注册、权限请求等功能
 */
class LoginActivity : BaseActivity() {
    
    // UI组件
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var edtUserID: EditText
    private lateinit var edtUserPass: EditText
    private lateinit var btnLogin: Button
    
    // 工具类
    private lateinit var utilAuth: UtilAuth
    private lateinit var backPressHandler: BackPressHandler
    
    // 协程作用域
    private val activityScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 权限请求码
    private companion object {
        private const val PERMISSIONS_REQUEST_CODE = 1001
        private const val OVERLAY_PERMISSION_REQ_CODE = 1002
        
        // 需要的权限列表
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.READ_CALL_LOG,
            Manifest.permission.WRITE_CALL_LOG,
            Manifest.permission.READ_CONTACTS,
            Manifest.permission.WRITE_CONTACTS,
            Manifest.permission.READ_SMS,
            Manifest.permission.RECEIVE_SMS,
            Manifest.permission.RECEIVE_BOOT_COMPLETED,
            Manifest.permission.INTERNET,
            Manifest.permission.ACCESS_NETWORK_STATE
        ).apply {
            // Android 10+ 需要额外权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                plus(arrayOf(
                    Manifest.permission.ANSWER_PHONE_CALLS,
                    Manifest.permission.READ_PHONE_NUMBERS
                ))
            }
        }
    }
    
    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            onPermissionsGranted()
        } else {
            showToast("应用需要所有权限才能正常工作")
            finish()
        }
    }
    
    // 悬浮窗权限请求启动器
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (Settings.canDrawOverlays(this)) {
                onOverlayPermissionGranted()
            } else {
                showToast("需要悬浮窗权限才能显示来电信息")
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)
        
        initViews()
        initUtils()
        requestPermissions()
        loadSavedCredentials()
    }
    
    /**
     * 初始化视图组件
     */
    private fun initViews() {
        coordinatorLayout = findViewById(R.id.coordinatorLayout)
        edtUserID = findViewById(R.id.edtLogID)
        edtUserPass = findViewById(R.id.edtLogPass)
        btnLogin = findViewById(R.id.btnLogin)
        
        // 初始状态禁用输入
        edtUserID.isEnabled = false
        edtUserPass.isEnabled = false
        btnLogin.isEnabled = false
    }
    
    /**
     * 初始化工具类
     */
    private fun initUtils() {
        utilAuth = UtilAuth.getInstance(this)
        backPressHandler = BackPressHandler(this)
    }
    
    /**
     * 请求权限
     */
    private fun requestPermissions() {
        val missingPermissions = REQUIRED_PERMISSIONS.filter { permission ->
            ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isNotEmpty()) {
            permissionLauncher.launch(missingPermissions.toTypedArray())
        } else {
            onPermissionsGranted()
        }
    }
    
    /**
     * 权限授予后的处理
     */
    private fun onPermissionsGranted() {
        // 检查悬浮窗权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
            requestOverlayPermission()
        } else {
            onOverlayPermissionGranted()
        }
    }
    
    /**
     * 请求悬浮窗权限
     */
    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:$packageName")
            }
            overlayPermissionLauncher.launch(intent)
        }
    }
    
    /**
     * 悬浮窗权限授予后的处理
     */
    private fun onOverlayPermissionGranted() {
        // 启用UI组件
        edtUserID.isEnabled = true
        edtUserPass.isEnabled = true
        btnLogin.isEnabled = true
        
        // 检查更新
        checkUpdate()
    }
    
    /**
     * 加载保存的登录凭据
     */
    private fun loadSavedCredentials() {
        edtUserID.setText(utilAuth.userEmail)
        edtUserPass.setText(utilAuth.userPWD)
    }
    
    /**
     * 登录按钮点击事件
     */
    fun onClickBtnLogin(view: View) {
        val userID = edtUserID.text.toString().trim()
        val userPass = edtUserPass.text.toString().trim()
        
        when {
            userID.isEmpty() -> showToast("请输入用户ID")
            userPass.isEmpty() -> showToast("请输入密码")
            else -> login(userID, userPass)
        }
    }
    
    /**
     * 执行登录
     */
    private fun login(userID: String, userPass: String) {
        activityScope.launch {
            try {
                showProgress(getString(R.string.wait))
                
                val userInfo = UserInfo(
                    userID = userID,
                    userPass = userPass,
                    devToken = utilAuth.getDeviceToken()
                )
                
                val result = loginWithEmail(userInfo)
                
                dismissProgress()
                
                if (result.isSuccess) {
                    onLoginSuccess()
                } else {
                    val error = result.exceptionOrNull()?.message ?: "登录失败"
                    showToast(error)
                }
                
            } catch (e: Exception) {
                dismissProgress()
                UtilLogFile.getInstance(this@LoginActivity).writeErrorLog("LoginActivity", "Login error", e)
                showToast("登录过程中发生错误")
            }
        }
    }
    
    /**
     * 使用邮箱登录
     */
    private suspend fun loginWithEmail(userInfo: UserInfo): Result<String> = withContext(Dispatchers.IO) {
        try {
            val jsonObject = JSONObject().apply {
                put("UserID", userInfo.userID)
                put("UserPWD", userInfo.userPass)
                put("DeviceToken", userInfo.devToken)
                put("Version", 1029)
            }
            
            val encryptedData = RC4.encrypt(jsonObject.toString(), Const.AUTH_KEY)
            val url = "${Utils.getServerUrl()}${Const.API_USER_LOGIN}&data=${Uri.encode(encryptedData)}"
            
            // 这里需要使用实际的网络请求库（如Retrofit）
            // 暂时返回模拟结果
            delay(2000) // 模拟网络延迟
            
            // 模拟登录成功
            utilAuth.apply {
                userEmail = userInfo.userID
                userPWD = userInfo.userPass
                userToken = "mock_token_${System.currentTimeMillis()}"
                userCompany = "测试公司"
                setRemainMinutes(30 * 24 * 60) // 30天
                saveAuthInfo()
            }
            
            Result.success("登录成功")
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 登录成功处理
     */
    private fun onLoginSuccess() {
        // 启动主服务
        startMyService()
        
        // 跳转到主界面
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
        finish()
    }
    
    /**
     * 检查应用更新
     */
    private fun checkUpdate() {
        activityScope.launch {
            try {
                // 这里可以实现检查更新的逻辑
                // 暂时跳过
                autoStart()
            } catch (e: Exception) {
                UtilLogFile.getInstance(this@LoginActivity).writeErrorLog("LoginActivity", "Check update error", e)
                autoStart()
            }
        }
    }
    
    /**
     * 自动启动
     */
    private fun autoStart() {
        dismissProgress()
        startMyService()
        
        if (utilAuth.isHaveToken()) {
            // 如果有有效令牌，直接跳转到主界面
            val intent = Intent(this, MainActivity::class.java)
            startActivity(intent)
            overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
            finish()
        }
    }
    
    override fun onPostResume() {
        super.onPostResume()
        Global.incomingCallNumber = ""
    }
    
    override fun onBackPressed() {
        backPressHandler.onBackPressed("再次点击退出应用")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        activityScope.cancel()
        backPressHandler.cancelToast()
    }
}
