package com.developer.oasis.model

import com.developer.oasis.data.RecentIncomeInfo

/**
 * 通话记录监听器接口
 * 用于监听通话记录变化的回调
 */
interface CallLogListener {
    /**
     * 通话记录变化时的回调方法
     * @param resultCode 结果代码
     * @param callList 通话记录列表
     */
    fun onResult(resultCode: Int, callList: List<RecentIncomeInfo>)
    
    /**
     * 通话记录发生变化
     */
    fun onCallLogChanged() {
        // 默认实现，子类可以重写
    }
}
