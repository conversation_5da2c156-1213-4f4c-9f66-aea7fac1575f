package com.developer.oasis.model

/**
 * 应用自定义异常类
 * 用于统一异常处理和错误信息管理
 */
class MyException : Exception {
    
    /**
     * 默认构造函数
     */
    constructor() : super()
    
    /**
     * 带消息的构造函数
     * @param message 异常消息
     */
    constructor(message: String) : super(message)
    
    /**
     * 带消息和原因的构造函数
     * @param message 异常消息
     * @param cause 异常原因
     */
    constructor(message: String, cause: Throwable) : super(message, cause)
    
    /**
     * 带原因的构造函数
     * @param cause 异常原因
     */
    constructor(cause: Throwable) : super(cause)
}
