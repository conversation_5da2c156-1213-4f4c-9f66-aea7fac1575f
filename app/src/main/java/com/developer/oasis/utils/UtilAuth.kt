package com.developer.oasis.utils

import android.content.Context
import android.provider.Settings
import java.text.SimpleDateFormat
import java.util.*

/**
 * 用户认证和会话管理工具类
 */
class UtilAuth private constructor(private val context: Context) {
    
    // 用户信息
    var userID: Int = 0
    var userToken: String = ""
    var userEmail: String = ""
    var userPWD: String = ""
    var userCompany: String = ""
    
    private var userRemainMinutes: Int = 0
    private var licenseEndDate: String = ""
    
    companion object {
        @Volatile
        private var INSTANCE: UtilAuth? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): UtilAuth {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UtilAuth(context.applicationContext).also { 
                    INSTANCE = it
                    it.loadAuthInfo()
                }
            }
        }
    }
    
    /**
     * 检查是否有有效的令牌
     */
    fun isHaveToken(): Boolean {
        return userToken.isNotEmpty()
    }
    
    /**
     * 获取剩余分钟数
     */
    fun getRemainMinutes(): Int {
        return userRemainMinutes
    }
    
    /**
     * 获取许可证结束日期
     */
    fun getLicenseEndDate(): String {
        return licenseEndDate
    }
    
    /**
     * 设置剩余分钟数并计算许可证结束日期
     */
    fun setRemainMinutes(minutes: Int) {
        userRemainMinutes = minutes
        val calendar = Calendar.getInstance().apply {
            add(Calendar.MINUTE, userRemainMinutes)
        }
        licenseEndDate = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            .format(calendar.time)
    }
    
    /**
     * 保存认证信息到SharedPreferences
     */
    fun saveAuthInfo() {
        UtilSharedPref.setString(context, Const.USER_TOKEN, userToken)
        UtilSharedPref.setString(context, Const.USER_ID, userEmail)
        UtilSharedPref.setString(context, Const.USER_PWD, userPWD)
        UtilSharedPref.setString(context, Const.USER_COMPANY, userCompany)
        UtilSharedPref.setInt(context, Const.REMAIN_MINUTES, userRemainMinutes)
        UtilSharedPref.setString(context, Const.LICENSEEND_DATE, licenseEndDate)
    }
    
    /**
     * 从SharedPreferences加载认证信息
     */
    fun loadAuthInfo() {
        userEmail = UtilSharedPref.getString(context, Const.USER_ID, "")
        userPWD = UtilSharedPref.getString(context, Const.USER_PWD, "")
        userToken = UtilSharedPref.getString(context, Const.USER_TOKEN, "")
        userCompany = UtilSharedPref.getString(context, Const.USER_COMPANY, "")
        userRemainMinutes = UtilSharedPref.getInt(context, Const.REMAIN_MINUTES, 1)
        licenseEndDate = UtilSharedPref.getString(context, Const.LICENSEEND_DATE, "")
    }
    
    /**
     * 获取设备令牌
     */
    fun getDeviceToken(): String {
        return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: ""
    }
    
    /**
     * 清除认证信息
     */
    fun clearAuthInfo() {
        userToken = ""
        userEmail = ""
        userPWD = ""
        userCompany = ""
        userRemainMinutes = 0
        licenseEndDate = ""
        saveAuthInfo()
    }
}
