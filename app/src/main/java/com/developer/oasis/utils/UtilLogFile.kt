package com.developer.oasis.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.OutputStreamWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日志文件管理工具类
 */
class UtilLogFile private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "UtilLogFile"
        
        @Volatile
        private var INSTANCE: UtilLogFile? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): UtilLogFile {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UtilLogFile(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * 获取日志文件夹路径
     */
    private fun getLogFolder(): String {
        val logPath = "${context.filesDir}/logs"
        val logDir = File(logPath)
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        return logPath
    }
    
    /**
     * 获取当前日志文件名
     */
    private fun getLogFileName(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val dateString = dateFormat.format(Date(System.currentTimeMillis()))
        return "${getLogFolder()}/log_$dateString.txt"
    }
    
    /**
     * 写入日志到文件
     */
    fun writeLog(message: String) {
        try {
            // 同时写入Android Log
            Log.d(TAG, message)
            
            // 异步写入文件
            CoroutineScope(Dispatchers.IO).launch {
                writeLogToFile(message)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write log", e)
        }
    }
    
    /**
     * 写入日志到文件（同步方法）
     */
    @Synchronized
    private suspend fun writeLogToFile(message: String) = withContext(Dispatchers.IO) {
        try {
            val logFile = File(getLogFileName())
            if (!logFile.exists()) {
                logFile.createNewFile()
            }
            
            FileOutputStream(logFile, true).use { fos ->
                OutputStreamWriter(fos, Charsets.UTF_8).use { writer ->
                    val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                    val timestamp = timeFormat.format(Date(System.currentTimeMillis()))
                    
                    writer.write("$timestamp\n")
                    writer.write(message)
                    writer.write("\n")
                    writer.flush()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to write log to file", e)
        }
    }
    
    /**
     * 写入错误日志
     */
    fun writeErrorLog(tag: String, message: String, throwable: Throwable? = null) {
        val logMessage = if (throwable != null) {
            "[$tag] ERROR: $message\n${Log.getStackTraceString(throwable)}"
        } else {
            "[$tag] ERROR: $message"
        }
        writeLog(logMessage)
    }
    
    /**
     * 写入信息日志
     */
    fun writeInfoLog(tag: String, message: String) {
        writeLog("[$tag] INFO: $message")
    }
    
    /**
     * 写入调试日志
     */
    fun writeDebugLog(tag: String, message: String) {
        writeLog("[$tag] DEBUG: $message")
    }
    
    /**
     * 清理旧日志文件（保留最近7天）
     */
    fun cleanOldLogs() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val logDir = File(getLogFolder())
                val files = logDir.listFiles { file ->
                    file.name.startsWith("log_") && file.name.endsWith(".txt")
                }
                
                files?.forEach { file ->
                    val fileTime = file.lastModified()
                    val currentTime = System.currentTimeMillis()
                    val daysDiff = (currentTime - fileTime) / (24 * 60 * 60 * 1000)
                    
                    if (daysDiff > 7) {
                        file.delete()
                        Log.d(TAG, "Deleted old log file: ${file.name}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to clean old logs", e)
            }
        }
    }
    
    /**
     * 检查外部存储是否可写
     */
    private fun isExternalStorageWritable(): Boolean {
        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
    }
    
    /**
     * 检查外部存储是否可读
     */
    private fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return state == Environment.MEDIA_MOUNTED || state == Environment.MEDIA_MOUNTED_READ_ONLY
    }
}
