package com.developer.oasis.utils

import android.annotation.SuppressLint
import android.app.ProgressDialog
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.telecom.TelecomManager
import android.telephony.TelephonyManager
import android.widget.Toast
import com.developer.oasis.data.NoticeInfo
import com.developer.oasis.data.PhoneInfo
import com.developer.oasis.data.RecentIncomeInfo
import kotlinx.coroutines.*
import java.lang.reflect.Method
import java.text.SimpleDateFormat
import java.util.*

/**
 * 通用工具函数类
 */
class Utils private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: Utils? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(): Utils {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: Utils().also { INSTANCE = it }
            }
        }
        
        /**
         * 检查字符串是否为空或null
         */
        fun isNullOrEmptyString(str: String?): Boolean {
            return str.isNullOrEmpty()
        }
        
        /**
         * 获取服务器URL
         */
        fun getServerUrl(): String {
            return "http://paker112.com/api/Zn/"
        }
        
        /**
         * 显示Toast消息
         */
        fun showToast(context: Context, message: String) {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
        
        /**
         * 格式化电话号码
         */
        fun getCorrectPhoneNumber(phoneNumber: String): String {
            var corrected = phoneNumber.trim()
            
            // 移除特殊字符
            corrected = corrected.replace(Regex("[^0-9+]"), "")
            
            // 处理韩国号码格式
            if (corrected.startsWith("+82")) {
                corrected = corrected.substring(3)
                if (corrected.startsWith("0")) {
                    corrected = corrected.substring(1)
                }
                corrected = "0$corrected"
            } else if (corrected.startsWith("82") && corrected.length > 10) {
                corrected = corrected.substring(2)
                if (!corrected.startsWith("0")) {
                    corrected = "0$corrected"
                }
            }
            
            return corrected
        }
    }
    
    /**
     * 创建进度对话框
     */
    fun openNewDialog(context: Context, message: String, cancelable: Boolean = false, indeterminate: Boolean = true): ProgressDialog {
        return ProgressDialog(context).apply {
            setMessage(message)
            setCancelable(cancelable)
            isIndeterminate = indeterminate
            setProgressStyle(ProgressDialog.STYLE_SPINNER)
        }
    }
    
    /**
     * 拒绝来电
     */
    @SuppressLint("MissingPermission")
    fun rejectCall(context: Context): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // Android 9.0及以上使用TelecomManager
                val telecomManager = context.getSystemService(Context.TELECOM_SERVICE) as TelecomManager
                telecomManager.endCall()
                true
            } else {
                // Android 9.0以下使用反射调用ITelephony
                val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
                val method: Method = telephonyManager.javaClass.getDeclaredMethod("getITelephony")
                method.isAccessible = true
                val iTelephony = method.invoke(telephonyManager)
                val endCallMethod = iTelephony.javaClass.getDeclaredMethod("endCall")
                endCallMethod.invoke(iTelephony)
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    /**
     * 获取新通知状态
     */
    fun getNewNoticeState(context: Context): Boolean {
        return UtilSharedPref.getBoolean(context, Const.MYPREFS_IsNewNotice, false)
    }
    
    /**
     * 设置新通知状态
     */
    fun setNewNoticeState(context: Context, hasNewNotice: Boolean) {
        UtilSharedPref.setBoolean(context, Const.MYPREFS_IsNewNotice, hasNewNotice)
    }
    
    /**
     * 格式化日期时间
     */
    fun formatDateTime(timestamp: Long, pattern: String = "yyyy-MM-dd HH:mm:ss"): String {
        return try {
            val formatter = SimpleDateFormat(pattern, Locale.getDefault())
            formatter.format(Date(timestamp))
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }
    
    /**
     * 解析日期字符串
     */
    fun parseDateTime(dateString: String, pattern: String = "yyyy-MM-dd HH:mm:ss"): Date? {
        return try {
            val formatter = SimpleDateFormat(pattern, Locale.getDefault())
            formatter.parse(dateString)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * 检查是否为今天
     */
    fun isToday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()
        val today = calendar.get(Calendar.DAY_OF_YEAR)
        val todayYear = calendar.get(Calendar.YEAR)
        
        calendar.timeInMillis = timestamp
        val targetDay = calendar.get(Calendar.DAY_OF_YEAR)
        val targetYear = calendar.get(Calendar.YEAR)
        
        return today == targetDay && todayYear == targetYear
    }
}
