package com.developer.oasis.utils

/**
 * 应用常量定义
 */
object Const {
    // API 端点
    const val API_ANNOUNCEMENTS = "GetAnnouncementList?\$format=json"
    const val API_BLOCK_CONFIG = "ReportBlockAllFlag?\$format=json"
    const val API_CALL_RESULT = "ReportPhoneCallResult?\$format=json"
    const val API_CHECK_NOTICE = "CheckNewNotice?\$format=json"
    const val API_CHECK_UPDATE = "CheckApkUpdate"
    const val API_GETQUERYLIMIT_COUNT = "GetQueryLimitCount?\$format=json"
    const val API_QUERYPHONENUMBER = "QueryPhoneNumber?data="
    const val API_UPDATEPHONENUMBER = "UpdatePhoneNumber?\$format=json"
    const val API_UPDATEPHONENUMBER_UNKNOWN = "UpdatePhoneNumber_Unknown?\$format=json"
    const val API_USER_LOGIN = "Authenticate?\$format=json"
    
    // 加密密钥
    const val AUTH_KEY = "#\$%f5ekkKKtriERT((89678\$8uKL%9"
    
    // SharedPreferences 键名
    const val MYPREFS = "sp"
    const val LICENSEEND_DATE = "LicenseEndDate"
    const val REMAIN_MINUTES = "remainminutes"
    const val UPDATECONTACK_TICK = "updatecontact_tick"
    const val USER_COMPANY = "usercompany"
    const val USER_ID = "user_id"
    const val USER_PWD = "user_pwd"
    const val USER_TOKEN = "user_token"
    
    // 拦截设置相关键名
    const val MYPREFS_Block_CallExplosion = "MYPREFS_Block_CallExplosion"
    const val MYPREFS_Block_CallExplosionCount = "MYPREFS_Block_CallExplosionCount"
    const val MYPREFS_Block_History = "MYPREFS_Block_History"
    const val MYPREFS_Block_IsBlockAll = "MYPREFS_Block_IsBlockAll"
    const val MYPREFS_Block_IsBlockPrefix = "MYPREFS_Block_IsBlockPrefix"
    const val MYPREFS_Block_IsBlockSpecNumber = "MYPREFS_Block_IsBlockSpecNumber"
    const val MYPREFS_Block_IsBlockTodayCall = "MYPREFS_Block_IsBlockTodayCall"
    const val MYPREFS_Block_IsBlockUnknown = "MYPREFS_Block_IsBlockNoContact"
    const val MYPREFS_Block_LimitTodayCall = "MYPREFS_Block_LimitTodayCall"
    const val MYPREFS_Block_PrefNumbers = "MYPREFS_Block_PrefNumbers"
    const val MYPREFS_Block_SpecNumbers = "MYPREFS_Block_Numbers"
    
    // 通知相关键名
    const val MYPREFS_CheckNewNoticeTick = "MYPREFS_CheckNewNotickTick"
    const val MYPREFS_IsNewNotice = "MYPREFS_IsNewNotice"
    
    // 设置相关键名
    const val MYPREFS_Setting_PopupPos = "MYPREFS_Setting_PopupPos"
    const val MYPREFS_Setting_PopupRemain = "MYPREFS_Setting_PopupRemain"
    const val MYPREFS_Setting_ShowTodayCall = "MYPREFS_Setting_ShowTodayCall"
    
    // Handler 消息类型
    const val WHAT_ONDOWNLOADFINISH = 100
    const val WHAT_UPDATEURL_FAIL = 1
    const val WHAT_UPDATEURL_SUCCESS = 0
    
    // Fragment 状态
    const val FRAGMENT_STATE_MAIN = 0
    const val FRAGMENT_STATE_SEARCH = 1
    const val FRAGMENT_STATE_WEB = 2
    const val FRAGMENT_STATE_NOTICE = 3
    const val FRAGMENT_STATE_BLOCK = 4
    const val FRAGMENT_STATE_SETTING = 7
    
    // 通话类型
    const val CALL_TYPE_PHONE = 0
    const val CALL_TYPE_SMS = 1
    
    // 本地测试标志
    const val LOCALTEST = false
}
