package com.developer.oasis.utils

import android.content.Context

/**
 * 应用设置管理工具类
 */
class UtilSetting private constructor(private val context: Context) {
    
    // 设置项
    var todayShow: Boolean = false
    var popupPosition: Int = 1
    var popupRemain: Boolean = false
    
    companion object {
        @Volatile
        private var INSTANCE: UtilSetting? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): UtilSetting {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UtilSetting(context.applicationContext).also { 
                    INSTANCE = it
                    it.loadSetting()
                }
            }
        }
    }
    
    /**
     * 加载设置
     */
    private fun loadSetting() {
        val sharedPreferences = UtilSharedPref.getDefault(context)
        
        todayShow = sharedPreferences.getBoolean(Const.MYPREFS_Setting_ShowTodayCall, true)
        popupRemain = sharedPreferences.getBoolean(Const.MYPREFS_Setting_PopupRemain, false)
        popupPosition = sharedPreferences.getInt(Const.MYPREFS_Setting_PopupPos, 1)
    }
    
    /**
     * 保存设置
     */
    fun saveSetting() {
        val editor = UtilSharedPref.getDefault(context).edit()
        
        editor.putBoolean(Const.MYPREFS_Setting_ShowTodayCall, todayShow)
        editor.putBoolean(Const.MYPREFS_Setting_PopupRemain, popupRemain)
        editor.putInt(Const.MYPREFS_Setting_PopupPos, popupPosition)
        
        editor.apply()
    }
    
    /**
     * 重置为默认设置
     */
    fun resetToDefault() {
        todayShow = true
        popupPosition = 1
        popupRemain = false
        saveSetting()
    }
    
    /**
     * 获取弹窗位置描述
     */
    fun getPopupPositionDescription(): String {
        return when (popupPosition) {
            0 -> "左上角"
            1 -> "右上角"
            2 -> "左下角"
            3 -> "右下角"
            else -> "右上角"
        }
    }
    
    /**
     * 设置弹窗位置
     */
    fun setPopupPosition(position: Int) {
        if (position in 0..3) {
            popupPosition = position
            saveSetting()
        }
    }
}
