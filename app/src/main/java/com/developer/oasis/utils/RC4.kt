package com.developer.oasis.utils

import android.util.Base64
import java.io.UnsupportedEncodingException

/**
 * RC4加密解密工具类
 * 注意：RC4算法已被认为不安全，建议在生产环境中使用更安全的加密算法
 */
object RC4 {
    
    /**
     * 加密字符串
     * @param data 要加密的数据
     * @param key 加密密钥
     * @return Base64编码的加密结果
     */
    fun encrypt(data: String, key: String): String {
        return try {
            val dataBytes = data.toByteArray(Charsets.UTF_8)
            val keyBytes = key.toByteArray(Charsets.UTF_8)
            val encrypted = encrypt(dataBytes, keyBytes)
            Base64.encodeToString(encrypted, Base64.NO_WRAP)
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }
    
    /**
     * 解密字符串
     * @param encryptedData Base64编码的加密数据
     * @param key 解密密钥
     * @return 解密后的字符串
     */
    fun decrypt(encryptedData: String, key: String): String {
        return try {
            val encryptedBytes = Base64.decode(encryptedData, Base64.NO_WRAP)
            val keyBytes = key.toByteArray(Charsets.UTF_8)
            val decrypted = decrypt(encryptedBytes, keyBytes)
            String(decrypted, Charsets.UTF_8)
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
            ""
        } catch (e: Exception) {
            e.printStackTrace()
            ""
        }
    }
    
    /**
     * 加密字节数组
     * @param data 要加密的数据
     * @param key 加密密钥
     * @return 加密后的字节数组
     */
    fun encrypt(data: ByteArray, key: ByteArray): ByteArray {
        require(key.isNotEmpty() && key.size <= 256) { 
            "key must be between 1 and 256 bytes" 
        }
        
        val s = ShortArray(256)
        val k = ShortArray(256)
        val keyLength = key.size
        
        // 初始化S盒和K盒
        for (i in 0..255) {
            s[i] = i.toShort()
            k[i] = key[i % keyLength].toShort()
        }
        
        // 初始置换
        var j = 0
        for (i in 0..255) {
            j = (j + s[i] + k[i]) and 255
            val temp = s[i]
            s[i] = s[j]
            s[j] = temp
        }
        
        // 生成密钥流并加密
        val result = ByteArray(data.size)
        var i = 0
        j = 0
        
        for (n in data.indices) {
            i = (i + 1) and 255
            j = (j + s[i]) and 255
            val temp = s[j]
            s[j] = s[i]
            s[i] = temp
            val keyByte = s[(s[i] + s[j]) and 255]
            result[n] = (keyByte.toInt() xor data[n].toInt()).toByte()
        }
        
        return result
    }
    
    /**
     * 解密字节数组（RC4算法中加密和解密是相同的操作）
     * @param data 要解密的数据
     * @param key 解密密钥
     * @return 解密后的字节数组
     */
    fun decrypt(data: ByteArray, key: ByteArray): ByteArray {
        return encrypt(data, key)
    }
}
