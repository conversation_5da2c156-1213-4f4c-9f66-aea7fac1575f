package com.developer.oasis.utils

import android.app.Activity
import android.widget.Toast

/**
 * 返回按钮双击退出处理器
 */
class BackPressHandler(private val activity: Activity) {
    
    private var backKeyPressedTime: Long = 0
    private var toast: Toast? = null
    
    companion object {
        private const val DEFAULT_INTERVAL = 2000L // 默认间隔2秒
    }
    
    /**
     * 处理返回按钮点击（使用默认提示消息和间隔）
     */
    fun onBackPressed() {
        onBackPressed("再次点击返回键退出应用", DEFAULT_INTERVAL)
    }
    
    /**
     * 处理返回按钮点击（自定义提示消息）
     */
    fun onBackPressed(message: String) {
        onBackPressed(message, DEFAULT_INTERVAL)
    }
    
    /**
     * 处理返回按钮点击（自定义间隔时间）
     */
    fun onBackPressed(intervalMs: Long) {
        onBackPressed("再次点击返回键退出应用", intervalMs)
    }
    
    /**
     * 处理返回按钮点击（自定义提示消息和间隔时间）
     */
    fun onBackPressed(message: String, intervalMs: Long) {
        val currentTime = System.currentTimeMillis()
        
        if (currentTime > backKeyPressedTime + intervalMs) {
            // 第一次点击，显示提示
            backKeyPressedTime = currentTime
            showGuide(message)
        } else {
            // 在间隔时间内再次点击，退出应用
            activity.finish()
            toast?.cancel()
        }
    }
    
    /**
     * 显示退出提示
     */
    private fun showGuide(message: String) {
        toast?.cancel()
        toast = Toast.makeText(activity, message, Toast.LENGTH_SHORT)
        toast?.show()
    }
    
    /**
     * 取消当前显示的Toast
     */
    fun cancelToast() {
        toast?.cancel()
    }
    
    /**
     * 重置状态
     */
    fun reset() {
        backKeyPressedTime = 0
        toast?.cancel()
    }
}
