package com.developer.oasis.utils

import com.developer.oasis.data.NoticeInfo

/**
 * 全局状态管理对象
 */
object Global {
    /**
     * 当前来电号码
     */
    var incomingCallNumber: String = ""
    
    /**
     * 当前Fragment状态
     */
    var fragmentState: Int = 0
    
    /**
     * 搜索历史标志
     */
    var searchHistory: Boolean = false
    
    /**
     * 剩余查询次数
     */
    var remainQueryCount: Int = 0
    
    /**
     * 通知列表
     */
    val noticeList: MutableList<NoticeInfo> = mutableListOf()
}
