package com.developer.oasis.utils

import android.content.Context
import android.content.SharedPreferences
import com.developer.oasis.data.BlockNumberHistory
import com.developer.oasis.data.PhoneInfo
import java.util.*

/**
 * 来电拦截逻辑和配置管理工具类
 */
class UtilBlock private constructor(private val context: Context) {
    
    // 拦截号码列表
    val lstSpecNumbers: MutableList<String> = mutableListOf()
    val lstPrefNumbers: MutableList<String> = mutableListOf()
    val lstBlockHistory: MutableList<BlockNumberHistory> = mutableListOf()
    val lstCallExplosion: MutableList<String> = mutableListOf()
    
    // 拦截设置
    var callExplosionCount: Int = 1
    var isBlockUnknown: Boolean = false
    var isBlockTodayCall: Boolean = false
    var isBlockPrefNumbers: Boolean = false
    var isBlockSpecNumbers: Boolean = false
    var isBlockAll: Boolean = false
    var isBlockCallExp: Boolean = false
    var nBlockLimitTodayCall: Int = 5
    
    companion object {
        @Volatile
        private var INSTANCE: UtilBlock? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): UtilBlock {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UtilBlock(context.applicationContext).also { 
                    INSTANCE = it
                    it.loadSetting()
                }
            }
        }
    }
    
    /**
     * 加载拦截设置
     */
    fun loadSetting() {
        val sharedPreferences = UtilSharedPref.getDefault(context)
        
        isBlockUnknown = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockUnknown, false)
        isBlockPrefNumbers = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockPrefix, false)
        isBlockSpecNumbers = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockSpecNumber, false)
        isBlockTodayCall = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockTodayCall, false)
        isBlockAll = sharedPreferences.getBoolean(Const.MYPREFS_Block_IsBlockAll, false)
        isBlockCallExp = sharedPreferences.getBoolean(Const.MYPREFS_Block_CallExplosion, false)
        callExplosionCount = sharedPreferences.getInt(Const.MYPREFS_Block_CallExplosionCount, 1)
        nBlockLimitTodayCall = sharedPreferences.getInt(Const.MYPREFS_Block_LimitTodayCall, 5)
        
        lstSpecNumbers.clear()
        lstSpecNumbers.addAll(UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_SpecNumbers))
        
        lstPrefNumbers.clear()
        lstPrefNumbers.addAll(UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_PrefNumbers))
        
        lstCallExplosion.clear()
        lstCallExplosion.addAll(UtilSharedPref.getStringArray(sharedPreferences, Const.MYPREFS_Block_CallExplosion))
        
        loadBlockHistory(sharedPreferences)
    }
    
    /**
     * 保存拦截设置
     */
    fun saveSetting() {
        val editor = UtilSharedPref.getDefault(context).edit()
        
        editor.putBoolean(Const.MYPREFS_Block_IsBlockUnknown, isBlockUnknown)
        editor.putBoolean(Const.MYPREFS_Block_IsBlockPrefix, isBlockPrefNumbers)
        editor.putBoolean(Const.MYPREFS_Block_IsBlockSpecNumber, isBlockSpecNumbers)
        editor.putBoolean(Const.MYPREFS_Block_IsBlockTodayCall, isBlockTodayCall)
        editor.putBoolean(Const.MYPREFS_Block_IsBlockAll, isBlockAll)
        editor.putInt(Const.MYPREFS_Block_LimitTodayCall, nBlockLimitTodayCall)
        editor.putBoolean(Const.MYPREFS_Block_CallExplosion, isBlockCallExp)
        editor.putInt(Const.MYPREFS_Block_CallExplosionCount, callExplosionCount)
        
        UtilSharedPref.putStringArray(editor, Const.MYPREFS_Block_SpecNumbers, lstSpecNumbers)
        UtilSharedPref.putStringArray(editor, Const.MYPREFS_Block_PrefNumbers, lstPrefNumbers)
        UtilSharedPref.putStringArray(editor, Const.MYPREFS_Block_CallExplosion, lstCallExplosion)
        
        saveBlockHistory(editor)
        editor.apply()
    }
    
    /**
     * 添加到呼叫爆炸列表
     */
    fun addCallExpList(phoneNumber: String) {
        if (!lstCallExplosion.contains(phoneNumber)) {
            lstCallExplosion.add(phoneNumber)
        }
    }
    
    /**
     * 加载拦截历史
     */
    private fun loadBlockHistory(sharedPreferences: SharedPreferences) {
        lstBlockHistory.clear()
        val count = sharedPreferences.getInt("MYPREFS_Block_History_count", 0)
        
        for (i in 0 until count) {
            val phoneNumber = sharedPreferences.getString("MYPREFS_Block_History_phone_$i", "") ?: ""
            val type = sharedPreferences.getInt("MYPREFS_Block_History_type_$i", 0)
            val dateTick = sharedPreferences.getLong("MYPREFS_Block_History_time_$i", 0L)
            
            if (!isOldDate(dateTick)) {
                lstBlockHistory.add(BlockNumberHistory(phoneNumber, type, dateTick))
            }
        }
    }
    
    /**
     * 保存拦截历史
     */
    private fun saveBlockHistory(editor: SharedPreferences.Editor) {
        var validCount = 0
        
        lstBlockHistory.forEachIndexed { index, history ->
            if (!isOldDate(history.dateTick)) {
                editor.putString("MYPREFS_Block_History_phone_$validCount", history.number)
                editor.putInt("MYPREFS_Block_History_type_$validCount", history.type)
                editor.putLong("MYPREFS_Block_History_time_$validCount", history.dateTick)
                validCount++
            }
        }
        
        editor.putInt("MYPREFS_Block_History_count", validCount)
    }
    
    /**
     * 添加拦截历史记录
     */
    fun addBlockHistory(blockHistory: BlockNumberHistory) {
        lstBlockHistory.add(blockHistory)
        val editor = UtilSharedPref.getDefault(context).edit()
        saveBlockHistory(editor)
        editor.apply()
    }
    
    /**
     * 添加拦截历史记录
     */
    fun addBlockHistory(phoneNumber: String, type: Int, dateTick: Long) {
        addBlockHistory(BlockNumberHistory(phoneNumber, type, dateTick))
    }
    
    /**
     * 判断号码是否需要拦截
     * @param phoneNumber 电话号码
     * @return 拦截类型 (0=不拦截, 1=未知号码, 3=特定号码, 4=前缀, 5=全部, 6=呼叫爆炸)
     */
    fun isNeedBlock(phoneNumber: String): Int {
        try {
            // 如果设置了拦截所有来电
            if (isBlockAll) {
                return BlockNumberHistory.TYPE_ALL
            }

            // 检查是否在联系人中
            val contactsList = UtilContact.getInstance(context).getContactsListRaw(false)
            val isInContacts = contactsList.any { it.phoneNumber == phoneNumber }

            if (!isInContacts) {
                // 未知号码拦截
                if (isBlockUnknown) {
                    return BlockNumberHistory.TYPE_UNKNOWN
                }

                // 特定号码拦截
                if (isBlockSpecNumbers) {
                    if (lstSpecNumbers.contains(phoneNumber)) {
                        return BlockNumberHistory.TYPE_SPECNUM
                    }
                }

                // 前缀拦截
                if (isBlockPrefNumbers) {
                    for (prefix in lstPrefNumbers) {
                        if (phoneNumber.startsWith(prefix)) {
                            return BlockNumberHistory.TYPE_PREFNUM
                        }
                    }
                }

                // 呼叫爆炸拦截
                if (isBlockCallExp) {
                    if (lstCallExplosion.contains(phoneNumber)) {
                        return BlockNumberHistory.TYPE_CALLEXP
                    }
                }
            }

            return 0 // 不拦截
        } catch (e: Exception) {
            e.printStackTrace()
            return 0
        }
    }

    /**
     * 检查是否为旧日期
     */
    private fun isOldDate(dateTick: Long): Boolean {
        val historyDate = Date(dateTick)
        val currentDate = Date()
        return historyDate.date < currentDate.date
    }
}
