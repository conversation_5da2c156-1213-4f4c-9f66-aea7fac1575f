package com.developer.oasis.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * SharedPreferences工具类
 */
object UtilSharedPref {
    
    /**
     * 获取默认的SharedPreferences实例
     */
    fun getDefault(context: Context): SharedPreferences {
        return context.getSharedPreferences(Const.MYPREFS, Context.MODE_PRIVATE)
    }
    
    /**
     * 保存布尔值
     */
    fun setBoolean(context: Context, key: String, value: Boolean) {
        getDefault(context).edit().apply {
            putBoolean(key, value)
            apply()
        }
    }
    
    /**
     * 获取布尔值
     */
    fun getBoolean(context: Context, key: String, defaultValue: Boolean = false): <PERSON><PERSON>an {
        return getDefault(context).getBoolean(key, defaultValue)
    }
    
    /**
     * 保存整数值
     */
    fun setInt(context: Context, key: String, value: Int) {
        getDefault(context).edit().apply {
            putInt(key, value)
            apply()
        }
    }
    
    /**
     * 获取整数值
     */
    fun getInt(context: Context, key: String, defaultValue: Int = 0): Int {
        return getDefault(context).getInt(key, defaultValue)
    }
    
    /**
     * 保存长整数值
     */
    fun setLong(context: Context, key: String, value: Long) {
        getDefault(context).edit().apply {
            putLong(key, value)
            apply()
        }
    }
    
    /**
     * 获取长整数值
     */
    fun getLong(context: Context, key: String, defaultValue: Long = 0L): Long {
        return getDefault(context).getLong(key, defaultValue)
    }
    
    /**
     * 保存字符串值
     */
    fun setString(context: Context, key: String, value: String) {
        getDefault(context).edit().apply {
            putString(key, value)
            apply()
        }
    }
    
    /**
     * 获取字符串值
     */
    fun getString(context: Context, key: String, defaultValue: String = ""): String {
        return getDefault(context).getString(key, defaultValue) ?: defaultValue
    }
    
    /**
     * 保存字符串数组
     */
    fun putStringArray(context: Context, key: String, list: List<String>?) {
        if (list == null) return
        
        getDefault(context).edit().apply {
            putInt("${key}_count", list.size)
            list.forEachIndexed { index, value ->
                putString("$key$index", value)
            }
            apply()
        }
    }
    
    /**
     * 获取字符串数组
     */
    fun getStringArray(context: Context, key: String): MutableList<String> {
        return getStringArray(getDefault(context), key)
    }
    
    /**
     * 从SharedPreferences获取字符串数组
     */
    fun getStringArray(sharedPreferences: SharedPreferences, key: String): MutableList<String> {
        val list = mutableListOf<String>()
        val count = sharedPreferences.getInt("${key}_count", 0)
        for (i in 0 until count) {
            val value = sharedPreferences.getString("$key$i", "") ?: ""
            list.add(value)
        }
        return list
    }
    
    /**
     * 使用Editor保存字符串数组
     */
    fun putStringArray(editor: SharedPreferences.Editor, key: String, list: List<String>?) {
        if (list == null) return
        
        editor.putInt("${key}_count", list.size)
        list.forEachIndexed { index, value ->
            editor.putString("$key$index", value)
        }
    }
}
