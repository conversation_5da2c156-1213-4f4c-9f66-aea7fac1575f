package com.developer.oasis.utils

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Handler
import android.provider.ContactsContract
import com.developer.oasis.data.PhoneInfo
import kotlinx.coroutines.*
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

/**
 * 联系人管理和服务器同步工具类
 */
class UtilContact private constructor(private val context: Context) {
    
    private var updateContactTick: Long = 0
    private var sendContactsToServerTickLast: Long = 0
    private var lstContact: MutableList<PhoneInfo>? = null
    
    companion object {
        const val QUERYTYPE_ONQUERY = 0
        const val QUERYTYPE_ONCALL = 1
        const val QUERYTYPE_ONHISTORY = 2
        
        @Volatile
        private var INSTANCE: UtilContact? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(context: Context): UtilContact {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: UtilContact(context.applicationContext).also { 
                    INSTANCE = it
                    it.init()
                }
            }
        }
    }
    
    /**
     * 初始化
     */
    private fun init() {
        updateContactTick = UtilSharedPref.getLong(context, Const.UPDATECONTACK_TICK, 0L)
    }
    
    /**
     * 设置联系人更新时间戳
     */
    fun setUpdateContactTick(tick: Long) {
        updateContactTick = tick
        UtilSharedPref.setLong(context, Const.UPDATECONTACK_TICK, tick)
    }
    
    /**
     * 获取联系人列表（原始数据）
     */
    fun getContactsListRaw(forceRefresh: Boolean): List<PhoneInfo> {
        if (lstContact == null || forceRefresh) {
            loadContactsFromDevice()
        }
        return lstContact ?: emptyList()
    }
    
    /**
     * 从设备加载联系人
     */
    private fun loadContactsFromDevice() {
        lstContact = mutableListOf()
        
        val projection = arrayOf(
            ContactsContract.CommonDataKinds.Phone.CONTACT_ID,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER
        )
        
        val cursor: Cursor? = context.contentResolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            projection,
            null,
            null,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC"
        )
        
        cursor?.use {
            val idIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.CONTACT_ID)
            val nameIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
            val numberIndex = it.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
            
            while (it.moveToNext()) {
                val id = it.getInt(idIndex)
                val name = it.getString(nameIndex) ?: ""
                val number = it.getString(numberIndex) ?: ""
                
                val phoneInfo = PhoneInfo(
                    id = id,
                    phoneNumber = Utils.getCorrectPhoneNumber(number),
                    userName = name,
                    updatetime = System.currentTimeMillis()
                )
                
                lstContact?.add(phoneInfo)
            }
        }
    }
    
    /**
     * 查询电话号码信息
     */
    suspend fun queryPhoneNumber(
        phoneNumber: String,
        includeToday: Boolean,
        queryType: Int,
        todayCallLimit: Int
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            val userToken = UtilAuth.getInstance(context).userToken
            if (userToken.isEmpty()) {
                return@withContext Result.failure(Exception("Token is not valid"))
            }
            
            val jsonObject = JSONObject().apply {
                put("RandomValue", Random().nextInt())
                put("PhoneNumber", phoneNumber)
                put("QueryType", queryType)
                put("TodayCallLimitBlock", todayCallLimit)
                put("IsContainTodayCall", includeToday)
                put("Version", 1029) // BuildConfig.VERSION_CODE
            }
            
            val encryptedData = RC4.encrypt(jsonObject.toString(), Const.AUTH_KEY)
            val url = "${Utils.getServerUrl()}${Const.API_QUERYPHONENUMBER}${Uri.encode(encryptedData)}"
            
            // 这里需要使用实际的网络请求库（如Retrofit）
            // 暂时返回模拟结果
            Result.success("查询成功")
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 发送联系人到服务器
     */
    suspend fun sendContactsToServer(): Result<String> = withContext(Dispatchers.IO) {
        try {
            val contacts = getContactsListRaw(false)
            val jsonArray = JSONArray()
            
            contacts.forEach { contact ->
                val jsonObject = JSONObject().apply {
                    put("PhoneNumber", contact.phoneNumber)
                    put("UserName", contact.userName)
                }
                jsonArray.put(jsonObject)
            }
            
            val requestData = JSONObject().apply {
                put("ContactList", jsonArray)
                put("UpdateTick", System.currentTimeMillis())
            }
            
            val encryptedData = RC4.encrypt(requestData.toString(), Const.AUTH_KEY)
            
            // 这里需要使用实际的网络请求库发送数据
            // 暂时返回模拟结果
            sendContactsToServerTickLast = System.currentTimeMillis()
            Result.success("联系人同步成功")
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 检查是否需要更新联系人
     */
    fun needUpdateContacts(): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastUpdate = updateContactTick
        val timeDiff = currentTime - lastUpdate
        
        // 如果超过24小时则需要更新
        return timeDiff > 24 * 60 * 60 * 1000
    }
    
    /**
     * 获取联系人数量
     */
    fun getContactCount(): Int {
        return getContactsListRaw(false).size
    }
}
