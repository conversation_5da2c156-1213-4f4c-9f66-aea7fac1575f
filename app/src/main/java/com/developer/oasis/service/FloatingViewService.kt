package com.developer.oasis.service

import android.annotation.SuppressLint
import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.view.*
import android.widget.*
import androidx.core.app.NotificationCompat
import com.developer.oasis.R
import com.developer.oasis.utils.*
import kotlinx.coroutines.*
import java.util.*

/**
 * 悬浮窗服务
 * 在通话期间显示号码信息的覆盖UI
 */
class FloatingViewService : Service() {
    
    private var floatingView: View? = null
    private var windowManager: WindowManager? = null
    private var serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var callNumber: String = ""
    private var blinkTimer: Timer? = null
    
    // UI组件
    private var txtPhoneNumber: TextView? = null
    private var txtResultCount: TextView? = null
    private var txtResultPhoneNumber: TextView? = null
    private var txtNewNotice: TextView? = null
    private var btnDetail: Button? = null
    private var linearLayout: LinearLayout? = null
    private var scrollView: ScrollView? = null
    private var viewDetail: View? = null
    
    companion object {
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "FloatingViewChannel"
        
        const val ACTION_FOREGROUND_START = "ACTION_FOREGROUND_START"
        const val ACTION_FOREGROUND_STOP = "ACTION_FOREGROUND_STOP"
    }
    
    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_FOREGROUND_START -> {
                val phoneNumber = intent.getStringExtra("tel") ?: ""
                showFloatingView(phoneNumber)
            }
            ACTION_FOREGROUND_STOP -> {
                hideFloatingView()
                stopSelf()
            }
        }
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        hideFloatingView()
        serviceScope.cancel()
        blinkTimer?.cancel()
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "悬浮窗服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "通话期间显示号码信息"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 显示悬浮窗
     */
    @SuppressLint("InflateParams")
    private fun showFloatingView(phoneNumber: String) {
        if (floatingView != null) return
        
        callNumber = phoneNumber
        
        // 创建前台通知
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 创建悬浮窗布局
        floatingView = LayoutInflater.from(this).inflate(R.layout.layout_floating_view, null)
        
        // 初始化UI组件
        initViews()
        
        // 设置窗口参数
        val params = createWindowLayoutParams()
        
        try {
            windowManager?.addView(floatingView, params)
            
            // 设置触摸监听
            setupTouchListener(params)
            
            // 显示电话号码
            txtPhoneNumber?.text = phoneNumber
            
            // 查询号码信息
            queryPhoneNumberInfo(phoneNumber)
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(this).writeErrorLog("FloatingViewService", "Failed to show floating view", e)
        }
    }
    
    /**
     * 隐藏悬浮窗
     */
    private fun hideFloatingView() {
        floatingView?.let { view ->
            try {
                windowManager?.removeView(view)
            } catch (e: Exception) {
                UtilLogFile.getInstance(this).writeErrorLog("FloatingViewService", "Failed to hide floating view", e)
            }
        }
        floatingView = null
    }
    
    /**
     * 初始化视图组件
     */
    private fun initViews() {
        floatingView?.let { view ->
            txtPhoneNumber = view.findViewById(R.id.txtPhoneNumber)
            txtResultCount = view.findViewById(R.id.txtResultCount)
            txtResultPhoneNumber = view.findViewById(R.id.txtResultPhoneNumber)
            txtNewNotice = view.findViewById(R.id.txtNewNotice)
            btnDetail = view.findViewById(R.id.btnDetail)
            linearLayout = view.findViewById(R.id.linearLayout)
            scrollView = view.findViewById(R.id.scrollView)
            viewDetail = view.findViewById(R.id.viewDetail)
            
            // 设置按钮点击事件
            btnDetail?.setOnClickListener {
                toggleDetailView()
            }
        }
    }
    
    /**
     * 创建窗口布局参数
     */
    private fun createWindowLayoutParams(): WindowManager.LayoutParams {
        val type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            @Suppress("DEPRECATION")
            WindowManager.LayoutParams.TYPE_PHONE
        }
        
        return WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            type,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = getFloatingViewGravity()
        }
    }
    
    /**
     * 获取悬浮窗位置
     */
    private fun getFloatingViewGravity(): Int {
        val position = UtilSetting.getInstance(this).popupPosition
        return when (position) {
            0 -> Gravity.TOP or Gravity.START
            1 -> Gravity.TOP or Gravity.END
            2 -> Gravity.BOTTOM or Gravity.START
            3 -> Gravity.BOTTOM or Gravity.END
            else -> Gravity.TOP or Gravity.END
        }
    }
    
    /**
     * 设置触摸监听器
     */
    private fun setupTouchListener(params: WindowManager.LayoutParams) {
        floatingView?.setOnTouchListener(object : View.OnTouchListener {
            private var initialX = 0
            private var initialY = 0
            private var initialTouchX = 0f
            private var initialTouchY = 0f
            
            override fun onTouch(v: View?, event: MotionEvent?): Boolean {
                when (event?.action) {
                    MotionEvent.ACTION_DOWN -> {
                        initialX = params.x
                        initialY = params.y
                        initialTouchX = event.rawX
                        initialTouchY = event.rawY
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x = initialX + (event.rawX - initialTouchX).toInt()
                        params.y = initialY + (event.rawY - initialTouchY).toInt()
                        windowManager?.updateViewLayout(floatingView, params)
                        return true
                    }
                }
                return false
            }
        })
    }
    
    /**
     * 查询号码信息
     */
    private fun queryPhoneNumberInfo(phoneNumber: String) {
        serviceScope.launch {
            try {
                val utilContact = UtilContact.getInstance(this@FloatingViewService)
                val result = utilContact.queryPhoneNumber(
                    phoneNumber = phoneNumber,
                    includeToday = true,
                    queryType = UtilContact.QUERYTYPE_ONCALL,
                    todayCallLimit = 0
                )
                
                if (result.isSuccess) {
                    // 更新UI显示查询结果
                    updateQueryResult(result.getOrNull() ?: "")
                }
                
            } catch (e: Exception) {
                UtilLogFile.getInstance(this@FloatingViewService)
                    .writeErrorLog("FloatingViewService", "Query phone number failed", e)
            }
        }
    }
    
    /**
     * 更新查询结果显示
     */
    private fun updateQueryResult(result: String) {
        Handler(Looper.getMainLooper()).post {
            txtResultPhoneNumber?.text = callNumber
            txtResultCount?.text = "查询完成"
            
            // 可以根据实际查询结果解析并显示更多信息
            if (result.isNotEmpty()) {
                // 解析并显示查询结果
                displayQueryDetails(result)
            }
        }
    }
    
    /**
     * 显示查询详情
     */
    private fun displayQueryDetails(result: String) {
        // 这里可以根据实际的查询结果格式来解析和显示
        // 暂时显示基本信息
        viewDetail?.visibility = View.VISIBLE
    }
    
    /**
     * 切换详情视图显示/隐藏
     */
    private fun toggleDetailView() {
        viewDetail?.let { view ->
            view.visibility = if (view.visibility == View.VISIBLE) {
                View.GONE
            } else {
                View.VISIBLE
            }
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("通话信息显示")
            .setContentText("正在显示来电信息")
            .setSmallIcon(R.drawable.ic_notification)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * 开始闪烁效果
     */
    private fun startBlinking() {
        blinkTimer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    Handler(Looper.getMainLooper()).post {
                        txtNewNotice?.let { textView ->
                            textView.visibility = if (textView.visibility == View.VISIBLE) {
                                View.INVISIBLE
                            } else {
                                View.VISIBLE
                            }
                        }
                    }
                }
            }, 0, 500)
        }
    }
    
    /**
     * 停止闪烁效果
     */
    private fun stopBlinking() {
        blinkTimer?.cancel()
        blinkTimer = null
        txtNewNotice?.visibility = View.VISIBLE
    }
}
