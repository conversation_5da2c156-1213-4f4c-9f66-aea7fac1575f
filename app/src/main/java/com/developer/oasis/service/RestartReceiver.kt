package com.developer.oasis.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.developer.oasis.utils.UtilLogFile

/**
 * 服务重启广播接收器
 * 用于定时重启服务以确保服务持续运行
 */
class RestartReceiver : BroadcastReceiver() {
    
    companion object {
        const val ACTION_RESTART_ALARM_SERVICE = "ACTION_RESTART_ALARM_SERVICE"
    }
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        when (intent.action) {
            ACTION_RESTART_ALARM_SERVICE -> {
                restartMainService(context)
                UtilLogFile.getInstance(context).writeInfoLog("RestartReceiver", "Alarm triggered, restarting MainService")
            }
        }
    }
    
    /**
     * 重启主服务
     */
    private fun restartMainService(context: Context) {
        try {
            val serviceIntent = Intent(context, MainService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(context).writeErrorLog("RestartReceiver", "Failed to restart MainService", e)
        }
    }
}
