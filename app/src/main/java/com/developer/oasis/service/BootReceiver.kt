package com.developer.oasis.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.developer.oasis.utils.UtilLogFile

/**
 * 系统启动广播接收器
 * 在设备启动完成后自动启动主服务
 */
class BootReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED -> {
                startMainService(context)
                UtilLogFile.getInstance(context).writeInfoLog("BootReceiver", "Device boot completed, starting MainService")
            }
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                if (intent.dataString?.contains(context.packageName) == true) {
                    startMainService(context)
                    UtilLogFile.getInstance(context).writeInfoLog("BootReceiver", "Package replaced, restarting MainService")
                }
            }
        }
    }
    
    /**
     * 启动主服务
     */
    private fun startMainService(context: Context) {
        try {
            val serviceIntent = Intent(context, MainService::class.java)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(context).writeErrorLog("BootReceiver", "Failed to start MainService", e)
        }
    }
}
