package com.developer.oasis.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.database.ContentObserver
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.provider.ContactsContract
import androidx.core.app.NotificationCompat
import com.developer.oasis.R
import com.developer.oasis.activity.MainActivity
import com.developer.oasis.utils.*
import kotlinx.coroutines.*
import java.util.*

/**
 * 应用核心后台服务
 * 负责联系人同步、通知检查、前台服务等功能
 */
class MainService : Service() {
    
    private val binder = LocalBinder()
    private var serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var timer: Timer? = null
    private var contactObserver: ContentObserver? = null
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "MainServiceChannel"
        private const val SYNC_INTERVAL = 30 * 60 * 1000L // 30分钟
    }
    
    inner class LocalBinder : Binder() {
        fun getService(): MainService = this@MainService
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
        
        // 注册联系人变化监听
        registerContactObserver()
        
        // 启动定时任务
        startPeriodicTasks()
        
        UtilLogFile.getInstance(this).writeInfoLog("MainService", "Service created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        UtilLogFile.getInstance(this).writeInfoLog("MainService", "Service started")
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onUnbind(intent: Intent?): Boolean {
        return true
    }
    
    override fun onRebind(intent: Intent?) {
        super.onRebind(intent)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // 清理资源
        serviceScope.cancel()
        timer?.cancel()
        timer = null
        
        // 取消注册联系人监听
        contactObserver?.let {
            contentResolver.unregisterContentObserver(it)
        }
        
        UtilLogFile.getInstance(this).writeInfoLog("MainService", "Service destroyed")
    }
    
    /**
     * 创建通知渠道（Android 8.0+）
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "来电拦截服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "来电拦截和号码查询后台服务"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("来电拦截服务")
            .setContentText("正在运行中...")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * 注册联系人变化监听器
     */
    private fun registerContactObserver() {
        contactObserver = object : ContentObserver(Handler()) {
            override fun onChange(selfChange: Boolean) {
                super.onChange(selfChange)
                // 联系人发生变化时同步到服务器
                serviceScope.launch {
                    syncContactsToServer()
                }
            }
        }
        
        contentResolver.registerContentObserver(
            ContactsContract.Contacts.CONTENT_URI,
            true,
            contactObserver!!
        )
    }
    
    /**
     * 启动定时任务
     */
    private fun startPeriodicTasks() {
        timer = Timer().apply {
            scheduleAtFixedRate(object : TimerTask() {
                override fun run() {
                    serviceScope.launch {
                        try {
                            // 检查新通知
                            checkNewNotice()
                            
                            // 同步联系人（如果需要）
                            val utilContact = UtilContact.getInstance(this@MainService)
                            if (utilContact.needUpdateContacts()) {
                                syncContactsToServer()
                            }
                            
                            // 清理旧日志
                            UtilLogFile.getInstance(this@MainService).cleanOldLogs()
                            
                        } catch (e: Exception) {
                            UtilLogFile.getInstance(this@MainService)
                                .writeErrorLog("MainService", "Periodic task error", e)
                        }
                    }
                }
            }, 0, SYNC_INTERVAL)
        }
    }
    
    /**
     * 检查新通知
     */
    private suspend fun checkNewNotice() = withContext(Dispatchers.IO) {
        try {
            // 这里需要实现实际的网络请求检查新通知
            // 暂时使用模拟逻辑
            val hasNewNotice = false // 从服务器获取
            Utils.getInstance().setNewNoticeState(this@MainService, hasNewNotice)
            
            UtilLogFile.getInstance(this@MainService)
                .writeDebugLog("MainService", "Checked new notice: $hasNewNotice")
                
        } catch (e: Exception) {
            UtilLogFile.getInstance(this@MainService)
                .writeErrorLog("MainService", "Failed to check new notice", e)
        }
    }
    
    /**
     * 同步联系人到服务器
     */
    private suspend fun syncContactsToServer() = withContext(Dispatchers.IO) {
        try {
            val utilContact = UtilContact.getInstance(this@MainService)
            val result = utilContact.sendContactsToServer()
            
            if (result.isSuccess) {
                utilContact.setUpdateContactTick(System.currentTimeMillis())
                UtilLogFile.getInstance(this@MainService)
                    .writeInfoLog("MainService", "Contacts synced successfully")
            } else {
                UtilLogFile.getInstance(this@MainService)
                    .writeErrorLog("MainService", "Failed to sync contacts: ${result.exceptionOrNull()?.message}")
            }
            
        } catch (e: Exception) {
            UtilLogFile.getInstance(this@MainService)
                .writeErrorLog("MainService", "Contact sync error", e)
        }
    }
    
    /**
     * 更新通知内容
     */
    fun updateNotification(title: String, content: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
            
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}
