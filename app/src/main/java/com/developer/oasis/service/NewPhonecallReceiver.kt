package com.developer.oasis.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.telephony.SmsMessage
import android.telephony.TelephonyManager
import com.developer.oasis.data.RecentIncomeInfo
import com.developer.oasis.fragment.MainFragment
import com.developer.oasis.utils.*
import kotlinx.coroutines.*
import java.util.*

/**
 * 来电/去电事件广播接收器
 * 负责监听通话状态变化并执行拦截逻辑
 */
class NewPhonecallReceiver : BroadcastReceiver() {
    
    private var context: Context? = null
    
    companion object {
        private var callStartTime: Date? = null
        private var isIncoming: Boolean = false
        private var lastState: Int = TelephonyManager.CALL_STATE_IDLE
        private var savedNumber: String? = null
        
        // 通话状态常量
        private const val CALL_STATE_IDLE = TelephonyManager.CALL_STATE_IDLE
        private const val CALL_STATE_RINGING = TelephonyManager.CALL_STATE_RINGING
        private const val CALL_STATE_OFFHOOK = TelephonyManager.CALL_STATE_OFFHOOK
    }
    
    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent == null) return
        
        this.context = context
        
        when (intent.action) {
            TelephonyManager.ACTION_PHONE_STATE_CHANGED -> {
                handlePhoneStateChanged(context, intent)
            }
            Intent.ACTION_NEW_OUTGOING_CALL -> {
                handleOutgoingCall(context, intent)
            }
            "android.provider.Telephony.SMS_RECEIVED" -> {
                handleSmsReceived(context, intent)
            }
        }
    }
    
    /**
     * 处理通话状态变化
     */
    private fun handlePhoneStateChanged(context: Context, intent: Intent) {
        val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)
        val phoneNumber = intent.getStringExtra(TelephonyManager.EXTRA_INCOMING_NUMBER)
        
        val callState = when (state) {
            TelephonyManager.EXTRA_STATE_IDLE -> CALL_STATE_IDLE
            TelephonyManager.EXTRA_STATE_RINGING -> CALL_STATE_RINGING
            TelephonyManager.EXTRA_STATE_OFFHOOK -> CALL_STATE_OFFHOOK
            else -> return
        }
        
        try {
            onCallStateChanged(context, callState, phoneNumber)
        } catch (e: Exception) {
            UtilLogFile.getInstance(context).writeErrorLog("NewPhonecallReceiver", "Call state change error", e)
        }
    }
    
    /**
     * 处理去电
     */
    private fun handleOutgoingCall(context: Context, intent: Intent) {
        val phoneNumber = intent.getStringExtra(Intent.EXTRA_PHONE_NUMBER)
        if (!phoneNumber.isNullOrEmpty()) {
            onOutgoingCallStarted(context, phoneNumber, Date())
        }
    }
    
    /**
     * 处理短信接收
     */
    private fun handleSmsReceived(context: Context, intent: Intent) {
        try {
            val bundle = intent.extras ?: return
            val pdus = bundle.get("pdus") as? Array<*> ?: return
            
            for (pdu in pdus) {
                val smsMessage = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    SmsMessage.createFromPdu(pdu as ByteArray, bundle.getString("format"))
                } else {
                    @Suppress("DEPRECATION")
                    SmsMessage.createFromPdu(pdu as ByteArray)
                }
                
                val phoneNumber = smsMessage.originatingAddress
                val messageBody = smsMessage.messageBody
                
                // 处理短信拦截逻辑
                if (!phoneNumber.isNullOrEmpty()) {
                    handleSmsBlock(context, phoneNumber, messageBody)
                }
            }
        } catch (e: Exception) {
            UtilLogFile.getInstance(context).writeErrorLog("NewPhonecallReceiver", "SMS handling error", e)
        }
    }
    
    /**
     * 通话状态变化处理
     */
    private fun onCallStateChanged(context: Context, state: Int, phoneNumber: String?) {
        if (lastState == state || phoneNumber.isNullOrEmpty()) {
            return
        }
        
        val correctedNumber = Utils.getCorrectPhoneNumber(phoneNumber)
        
        when (state) {
            CALL_STATE_RINGING -> {
                // 来电响铃
                handleIncomingCall(context, correctedNumber)
            }
            CALL_STATE_OFFHOOK -> {
                // 通话开始
                handleCallStarted(context, correctedNumber)
            }
            CALL_STATE_IDLE -> {
                // 通话结束
                handleCallEnded(context, correctedNumber)
            }
        }
        
        lastState = state
    }
    
    /**
     * 处理来电
     */
    private fun handleIncomingCall(context: Context, phoneNumber: String) {
        isIncoming = true
        savedNumber = phoneNumber
        Global.incomingCallNumber = phoneNumber
        
        // 检查是否需要拦截
        val blockType = UtilBlock.getInstance(context).isNeedBlock(phoneNumber)
        if (blockType > 0) {
            // 执行拦截
            val success = Utils.getInstance().rejectCall(context)
            if (success) {
                // 记录拦截历史
                UtilBlock.getInstance(context).addBlockHistory(phoneNumber, blockType, System.currentTimeMillis())
                
                UtilLogFile.getInstance(context).writeInfoLog(
                    "NewPhonecallReceiver", 
                    "Blocked incoming call from $phoneNumber, type: $blockType"
                )
            }
        } else {
            // 显示悬浮窗
            createFloatingUI(context, phoneNumber, "ACTION_FOREGROUND_START")
            
            // 查询号码信息
            queryPhoneNumberInfo(context, phoneNumber)
        }
    }
    
    /**
     * 处理通话开始
     */
    private fun handleCallStarted(context: Context, phoneNumber: String) {
        callStartTime = Date()
        
        if (!isIncoming) {
            // 去电开始
            onOutgoingCallStarted(context, phoneNumber, callStartTime!!)
        }
        
        // 发送通话结果到服务器
        CoroutineScope(Dispatchers.IO).launch {
            Utils.getInstance().sendCallResult(context, phoneNumber, Const.CALL_TYPE_PHONE)
        }
    }
    
    /**
     * 处理通话结束
     */
    private fun handleCallEnded(context: Context, phoneNumber: String) {
        // 隐藏悬浮窗
        createFloatingUI(context, phoneNumber, "ACTION_FOREGROUND_STOP")
        
        // 刷新通话记录
        refreshCallLog()
        
        // 重置状态
        Global.incomingCallNumber = ""
        isIncoming = false
        savedNumber = null
        callStartTime = null
    }
    
    /**
     * 去电开始处理
     */
    private fun onOutgoingCallStarted(context: Context, phoneNumber: String, startTime: Date) {
        UtilLogFile.getInstance(context).writeInfoLog(
            "NewPhonecallReceiver", 
            "Outgoing call started to $phoneNumber"
        )
        
        // 可以在这里添加去电相关的逻辑
    }
    
    /**
     * 处理短信拦截
     */
    private fun handleSmsBlock(context: Context, phoneNumber: String, messageBody: String) {
        val correctedNumber = Utils.getCorrectPhoneNumber(phoneNumber)
        val blockType = UtilBlock.getInstance(context).isNeedBlock(correctedNumber)
        
        if (blockType > 0) {
            // 记录短信拦截
            UtilBlock.getInstance(context).addBlockHistory(correctedNumber, blockType, System.currentTimeMillis())
            
            UtilLogFile.getInstance(context).writeInfoLog(
                "NewPhonecallReceiver", 
                "Blocked SMS from $correctedNumber, type: $blockType"
            )
        }
    }
    
    /**
     * 创建悬浮UI
     */
    private fun createFloatingUI(context: Context, phoneNumber: String, action: String) {
        val intent = Intent(context, FloatingViewService::class.java).apply {
            putExtra("tel", Utils.getCorrectPhoneNumber(phoneNumber))
            setAction(action)
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
        } else {
            context.startService(intent)
        }
    }
    
    /**
     * 查询号码信息
     */
    private fun queryPhoneNumberInfo(context: Context, phoneNumber: String) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val utilContact = UtilContact.getInstance(context)
                val utilSetting = UtilSetting.getInstance(context)
                val utilBlock = UtilBlock.getInstance(context)
                
                val result = utilContact.queryPhoneNumber(
                    phoneNumber = phoneNumber,
                    includeToday = utilSetting.todayShow,
                    queryType = UtilContact.QUERYTYPE_ONCALL,
                    todayCallLimit = if (utilBlock.isBlockTodayCall) utilBlock.nBlockLimitTodayCall else 0
                )
                
                // 处理查询结果
                if (result.isSuccess) {
                    UtilLogFile.getInstance(context).writeInfoLog(
                        "NewPhonecallReceiver", 
                        "Phone number query successful for $phoneNumber"
                    )
                }
            } catch (e: Exception) {
                UtilLogFile.getInstance(context).writeErrorLog(
                    "NewPhonecallReceiver", 
                    "Phone number query failed", e
                )
            }
        }
    }
    
    /**
     * 刷新通话记录
     */
    private fun refreshCallLog() {
        if (Global.fragmentState != Const.FRAGMENT_STATE_MAIN) {
            return
        }
        
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                MainFragment.getInstance()?.refreshCallLog()
            } catch (e: Exception) {
                context?.let {
                    UtilLogFile.getInstance(it).writeErrorLog("NewPhonecallReceiver", "Refresh call log error", e)
                }
            }
        }, 1000)
    }
}
