package com.developer.oasis.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.TextView
import com.developer.oasis.R
import com.developer.oasis.data.SearchResultData
import java.text.SimpleDateFormat
import java.util.*

/**
 * 搜索结果列表适配器
 * 用于显示电话号码搜索结果
 */
class SearchResultListAdapter(
    context: Context,
    private val items: List<SearchResultData>
) : ArrayAdapter<SearchResultData>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    
    // 响应类型图标
    private val responseTypeIcons = arrayOf(
        R.drawable.icon_none,    // 0 - 无响应
        R.drawable.icon_reject,  // 1 - 拒绝
        R.drawable.icon_accept,  // 2 - 接受
        <PERSON>.drawable.icon_miss,    // 3 - 未接
        R.drawable.icon_sms      // 4 - 短信
    )
    
    // 分类类型图标
    private val categoryTypeIcons = arrayOf(
        R.drawable.icon_0,   // 256 - 分类0
        R.drawable.icon_1,   // 257 - 分类1
        R.drawable.icon_2,   // 258 - 分类2
        R.drawable.icon_3,   // 259 - 分类3
        R.drawable.icon_4,   // 260 - 分类4
        R.drawable.icon_5,   // 261 - 分类5
        R.drawable.icon_6,   // 262 - 分类6
        R.drawable.icon_7,   // 263 - 分类7
        R.drawable.icon_8,   // 264 - 分类8
        R.drawable.icon_9,   // 265 - 分类9
        R.drawable.icon_10,  // 266 - 分类10
        R.drawable.icon_11,  // 267 - 分类11
        R.drawable.icon_12   // 268 - 分类12
    )
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_phone, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item)
        
        return view
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: SearchResultData) {
        val txtCompany = view.findViewById<TextView>(R.id.txtCompany)
        val txtContact = view.findViewById<TextView>(R.id.txtContact)
        val txtUpdateDate = view.findViewById<TextView>(R.id.txtUpdateDate)
        val txtUpdateTime = view.findViewById<TextView>(R.id.txtUpdateTime)
        val imgAction = view.findViewById<ImageView>(R.id.imgAction)
        
        // 设置公司名称
        txtCompany.text = item.compamy
        
        // 设置联系信息和颜色
        txtContact.text = item.memo
        txtContact.setTextColor(item.color)
        
        // 设置动作图标
        setActionIcon(imgAction, item.action)
        
        // 解析和设置日期时间
        parseAndSetDateTime(item.date, txtUpdateDate, txtUpdateTime)
    }
    
    /**
     * 设置动作图标
     */
    private fun setActionIcon(imageView: ImageView, action: Int) {
        try {
            val iconRes = when {
                action >= 256 -> {
                    // 分类类型图标
                    val index = action - 256
                    if (index < categoryTypeIcons.size) {
                        categoryTypeIcons[index]
                    } else {
                        categoryTypeIcons[0] // 默认图标
                    }
                }
                action < responseTypeIcons.size -> {
                    // 响应类型图标
                    responseTypeIcons[action]
                }
                else -> {
                    responseTypeIcons[0] // 默认图标
                }
            }
            imageView.setImageResource(iconRes)
        } catch (e: Exception) {
            e.printStackTrace()
            imageView.setImageResource(categoryTypeIcons[0]) // 设置默认图标
        }
    }
    
    /**
     * 解析并设置日期时间
     */
    private fun parseAndSetDateTime(
        dateString: String,
        dateTextView: TextView,
        timeTextView: TextView
    ) {
        try {
            // 尝试解析完整的日期时间格式
            val fullDateFormat = SimpleDateFormat("yyyy.MM.dd HH:mm:ss", Locale.getDefault())
            val date = fullDateFormat.parse(dateString)
            
            if (date != null) {
                // 格式化日期
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                dateTextView.text = dateFormat.format(date)
                
                // 格式化时间
                val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                timeTextView.text = "${timeFormat.format(date)}  "
            } else {
                // 如果解析失败，直接显示原始字符串
                dateTextView.text = dateString
                timeTextView.text = ""
            }
        } catch (e: Exception) {
            e.printStackTrace()
            // 解析失败时直接显示原始字符串
            dateTextView.text = dateString
            timeTextView.text = ""
        }
    }
}
