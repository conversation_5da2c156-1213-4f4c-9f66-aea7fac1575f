package com.developer.oasis.adapter

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.TextView
import com.developer.oasis.R
import com.developer.oasis.data.RecentCallData
import com.developer.oasis.utils.Const

/**
 * 最近通话记录适配器
 * 用于显示通话记录列表
 */
class RecentCallListAdapter(
    context: Context,
    private val items: List<RecentCallData>
) : ArrayAdapter<RecentCallData>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    
    // 通话类型图标
    private val callTypeIcons = arrayOf(
        R.drawable.ic_call,     // 电话
        R.drawable.ic_sms       // 短信
    )
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_recentcalllist, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item)
        
        return view
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: RecentCallData) {
        val btnCall = view.findViewById<View>(R.id.btnCall)
        val imgPhone = view.findViewById<ImageView>(R.id.imgPhone)
        val txtPhoneNumber = view.findViewById<TextView>(R.id.txtPhoneNumber)
        val txtDate = view.findViewById<TextView>(R.id.txtDate)
        val txtMemo = view.findViewById<TextView>(R.id.txtMemo)
        
        // 设置通话类型图标
        val iconIndex = if (item.callType < callTypeIcons.size) item.callType else 0
        imgPhone.setImageResource(callTypeIcons[iconIndex])
        
        // 设置文本信息
        txtPhoneNumber.text = item.phonenumber
        txtDate.text = item.date
        txtMemo.text = item.memo
        
        // 设置点击事件
        btnCall.setOnClickListener {
            when (item.callType) {
                Const.CALL_TYPE_PHONE -> makePhoneCall(item.phonenumber)
                Const.CALL_TYPE_SMS -> sendSms(item.phonenumber)
                else -> makePhoneCall(item.phonenumber) // 默认拨打电话
            }
        }
        
        // 设置标签
        view.tag = item
    }
    
    /**
     * 拨打电话
     */
    private fun makePhoneCall(phoneNumber: String) {
        try {
            val intent = Intent(Intent.ACTION_CALL, Uri.parse("tel:$phoneNumber"))
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
            // 如果没有拨号权限，使用拨号界面
            try {
                val dialIntent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phoneNumber"))
                context.startActivity(dialIntent)
            } catch (ex: Exception) {
                ex.printStackTrace()
            }
        }
    }
    
    /**
     * 发送短信
     */
    private fun sendSms(phoneNumber: String) {
        try {
            val intent = Intent(Intent.ACTION_SENDTO, Uri.parse("sms:$phoneNumber"))
            intent.putExtra("sms_body", "")
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
