package com.developer.oasis.adapter

import android.app.AlertDialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.developer.oasis.R
import com.developer.oasis.data.BlockNumberData
import com.developer.oasis.model.BlockNumberDeleteListener
import com.developer.oasis.utils.Utils

/**
 * 拦截号码列表适配器
 * 用于显示和管理拦截号码列表
 */
class BlockNumberListAdapter(
    context: Context,
    private val items: MutableList<BlockNumberData>
) : ArrayAdapter<BlockNumberData>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    private var deleteListener: BlockNumberDeleteListener? = null
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_blocklist, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item, position)
        
        return view
    }
    
    /**
     * 设置删除监听器
     */
    fun setOnDeleteListener(listener: BlockNumberDeleteListener) {
        deleteListener = listener
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: BlockNumberData, position: Int) {
        val txtPhone = view.findViewById<TextView>(R.id.txt_phone)
        val txtBlockCount = view.findViewById<TextView>(R.id.txt_blockcount)
        val containerLayout = view.findViewById<LinearLayout>(R.id.contanerlayout)
        val btnDelete = view.findViewById<View>(R.id.btnDelete)
        
        // 设置电话号码（格式化显示）
        txtPhone.text = formatPhoneNumber(item.phoneNumber)
        
        // 设置拦截次数
        txtBlockCount.text = item.nTodayCount.toString()
        
        // 根据拦截类型设置背景颜色
        val backgroundColorRes = when (item.nBlockType) {
            BlockNumberData.BLOCK_TYPE_PREF -> R.color.greenlight_alpha_color  // 前缀拦截
            else -> R.color.white_alpha_color  // 其他类型
        }
        containerLayout.setBackgroundColor(ContextCompat.getColor(context, backgroundColorRes))
        
        // 设置删除按钮点击事件
        if (deleteListener != null) {
            btnDelete.setOnClickListener {
                showDeleteConfirmDialog(item)
            }
            btnDelete.visibility = View.VISIBLE
        } else {
            btnDelete.visibility = View.GONE
        }
        
        // 设置标签
        view.tag = item
    }
    
    /**
     * 格式化电话号码显示
     */
    private fun formatPhoneNumber(phoneNumber: String): String {
        return Utils.getCorrectPhoneNumber(phoneNumber)
    }
    
    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(item: BlockNumberData) {
        AlertDialog.Builder(context)
            .setTitle("删除确认")
            .setMessage("确定要删除号码 ${item.phoneNumber} 吗？")
            .setPositiveButton("确定") { _, _ ->
                deleteListener?.onResult(item)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 添加拦截号码
     */
    fun addItem(item: BlockNumberData) {
        items.add(item)
        notifyDataSetChanged()
    }
    
    /**
     * 删除拦截号码
     */
    fun removeItem(item: BlockNumberData) {
        items.remove(item)
        notifyDataSetChanged()
    }
    
    /**
     * 更新拦截号码
     */
    fun updateItem(oldItem: BlockNumberData, newItem: BlockNumberData) {
        val index = items.indexOf(oldItem)
        if (index >= 0) {
            items[index] = newItem
            notifyDataSetChanged()
        }
    }
    
    /**
     * 清空所有项目
     */
    fun clearItems() {
        items.clear()
        notifyDataSetChanged()
    }
    
    /**
     * 获取所有项目
     */
    fun getAllItems(): List<BlockNumberData> {
        return items.toList()
    }
}
