package com.developer.oasis.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.ImageView
import android.widget.TextView
import com.developer.oasis.R
import com.developer.oasis.data.DrawerData
import com.developer.oasis.utils.Utils

/**
 * 导航抽屉适配器
 * 用于显示导航菜单项
 */
class DrawerAdapter(
    context: Context,
    private val items: List<DrawerData>
) : ArrayAdapter<DrawerData>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_drawer, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item, position)
        
        return view
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: DrawerData, position: Int) {
        val iconIV = view.findViewById<ImageView>(R.id.iconIV)
        val nameTV = view.findViewById<TextView>(R.id.nameTV)
        val txtNew = view.findViewById<TextView>(R.id.txtNew)
        
        // 设置图标
        item.icon?.let { iconRes ->
            iconIV.setImageResource(iconRes)
        }
        
        // 设置名称
        nameTV.text = item.name
        
        // 设置计数或新标记
        when {
            item.cnt.isNotEmpty() -> {
                txtNew.text = item.cnt
                txtNew.visibility = View.VISIBLE
            }
            position == 2 && Utils.getInstance().getNewNoticeState(context) -> {
                // 通知项显示"新"标记
                txtNew.text = "新"
                txtNew.visibility = View.VISIBLE
            }
            else -> {
                txtNew.visibility = View.GONE
            }
        }
    }
}
