package com.developer.oasis.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.developer.oasis.R
import com.developer.oasis.data.NoticeInfo

/**
 * 通知列表适配器
 * 用于显示应用通知和公告列表
 */
class NoticeListAdapter(
    context: Context,
    private val items: List<NoticeInfo>
) : ArrayAdapter<NoticeInfo>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_noticelist, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item)
        
        return view
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: NoticeInfo) {
        val txtNoticeTitle = view.findViewById<TextView>(R.id.txtNoticeTitle)
        val txtNoticeDate = view.findViewById<TextView>(R.id.txtNoticeDate)
        val txtNoticeContent = view.findViewById<TextView>(R.id.txtNoticeContent)
        
        // 设置通知标题
        txtNoticeTitle.text = item.subject
        
        // 设置通知日期
        txtNoticeDate.text = item.date
        
        // 设置通知内容（可能需要截断显示）
        txtNoticeContent.text = if (item.content.length > 100) {
            "${item.content.substring(0, 100)}..."
        } else {
            item.content
        }
    }
}
