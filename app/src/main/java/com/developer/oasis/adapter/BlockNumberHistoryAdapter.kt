package com.developer.oasis.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.developer.oasis.R
import com.developer.oasis.data.BlockNumberHistory
import com.developer.oasis.utils.Utils
import java.text.SimpleDateFormat
import java.util.*

/**
 * 拦截历史记录适配器
 * 用于显示拦截历史记录列表
 */
class BlockNumberHistoryAdapter(
    context: Context,
    private val items: List<BlockNumberHistory>
) : ArrayAdapter<BlockNumberHistory>(context, 0, items) {
    
    private val inflater = LayoutInflater.from(context)
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    
    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        val view = convertView ?: inflater.inflate(R.layout.adapter_blockhistory, parent, false)
        
        val item = getItem(position) ?: return view
        bindView(view, item)
        
        return view
    }
    
    /**
     * 绑定数据到视图
     */
    private fun bindView(view: View, item: BlockNumberHistory) {
        val txtPhone = view.findViewById<TextView>(R.id.txt_phone)
        val txtDate = view.findViewById<TextView>(R.id.txtDate)
        val txtBlockComment = view.findViewById<TextView>(R.id.txtBlockComment)
        
        // 设置电话号码（格式化显示）
        txtPhone.text = Utils.getCorrectPhoneNumber(item.number)
        
        // 设置拦截时间
        txtDate.text = dateFormat.format(Date(item.dateTick))
        
        // 设置拦截类型说明
        txtBlockComment.text = getBlockTypeDescription(item.type)
        
        // 设置标签
        view.tag = item
    }
    
    /**
     * 获取拦截类型描述
     */
    private fun getBlockTypeDescription(type: Int): String {
        return when (type) {
            BlockNumberHistory.TYPE_UNKNOWN -> "未知号码拦截"
            BlockNumberHistory.TYPE_TODAYCALL -> "今日通话限制拦截"
            BlockNumberHistory.TYPE_SPECNUM -> "特定号码拦截"
            BlockNumberHistory.TYPE_PREFNUM -> "前缀号码拦截"
            BlockNumberHistory.TYPE_ALL -> "全部号码拦截"
            BlockNumberHistory.TYPE_CALLEXP -> "呼叫爆炸拦截"
            else -> "未知类型"
        }
    }
    
    /**
     * 获取拦截类型颜色
     */
    private fun getBlockTypeColor(type: Int): Int {
        return when (type) {
            BlockNumberHistory.TYPE_UNKNOWN -> android.R.color.holo_orange_light
            BlockNumberHistory.TYPE_TODAYCALL -> android.R.color.holo_blue_light
            BlockNumberHistory.TYPE_SPECNUM -> android.R.color.holo_red_light
            BlockNumberHistory.TYPE_PREFNUM -> android.R.color.holo_green_light
            BlockNumberHistory.TYPE_ALL -> android.R.color.holo_purple
            BlockNumberHistory.TYPE_CALLEXP -> android.R.color.holo_red_dark
            else -> android.R.color.darker_gray
        }
    }
    
    /**
     * 按日期分组获取历史记录
     */
    fun getGroupedByDate(): Map<String, List<BlockNumberHistory>> {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return items.groupBy { history ->
            dateFormat.format(Date(history.dateTick))
        }
    }
    
    /**
     * 获取今日拦截记录
     */
    fun getTodayBlocks(): List<BlockNumberHistory> {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        return items.filter { history ->
            val historyDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(history.dateTick))
            historyDate == today
        }
    }
    
    /**
     * 按拦截类型统计
     */
    fun getBlockTypeStatistics(): Map<Int, Int> {
        return items.groupingBy { it.type }.eachCount()
    }
}
