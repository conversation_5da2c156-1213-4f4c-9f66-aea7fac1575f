package com.developer.oasis

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.*
import androidx.test.espresso.assertion.ViewAssertions.*
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.developer.oasis.activity.LoginActivity
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * 主Activity集成测试
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(LoginActivity::class.java)
    
    @Test
    fun testLoginActivityDisplayed() {
        // 验证登录界面是否正确显示
        onView(withId(R.id.edtLogID))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.edtLogPass))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.btnLogin))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testLoginFormValidation() {
        // 测试空用户名验证
        onView(withId(R.id.btnLogin))
            .perform(click())
        
        // 输入用户名但不输入密码
        onView(withId(R.id.edtLogID))
            .perform(typeText("<EMAIL>"), closeSoftKeyboard())
        
        onView(withId(R.id.btnLogin))
            .perform(click())
        
        // 输入完整的登录信息
        onView(withId(R.id.edtLogPass))
            .perform(typeText("password123"), closeSoftKeyboard())
        
        onView(withId(R.id.btnLogin))
            .perform(click())
    }
    
    @Test
    fun testUIElementsInteraction() {
        // 测试输入框交互
        onView(withId(R.id.edtLogID))
            .perform(typeText("<EMAIL>"))
            .check(matches(withText("<EMAIL>")))
        
        onView(withId(R.id.edtLogPass))
            .perform(typeText("password123"))
            .check(matches(withText("password123")))
        
        // 清除输入
        onView(withId(R.id.edtLogID))
            .perform(clearText())
            .check(matches(withText("")))
    }
}
